// iops信息
declare type MonitorInfo<T = any> = {
	id: number;
	timestamp: number; // 时间戳，单位秒
	disk_r_bytes: number; // 磁盘读速率 b/s
	disk_w_bytes: number; // 磁盘写速率 b/s
	disk_r_ops: number; // 磁盘读iops 次
	disk_w_ops: number;// 磁盘写iops 次
	cpu_usage_rate: number; // cpu使用率
	mem_usage_rate: number; // 内存使用率
	net_rx_bytes: number; // 网卡读速率 b/s
	net_wx_bytes: number; // 网卡写速率 b/s
	fc_rx_bytes: number; // 光纤卡读速率 b/s
	fc_wx_bytes: number; // 光纤卡写速率 b/s
	dedupe_rate: number; // 重删率 %
};
