import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Session } from '/@/utils/storage';
import qs from 'qs';

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	timeout: 50000,
	headers: { 'Content-Type': 'application/json' },
	paramsSerializer: {
		serialize(params) {
			return qs.stringify(params, { allowDots: true });
		},
	},
});

// 添加请求拦截器
service.interceptors.request.use(
	(config) => {
		// 在发送请求之前做些什么 token
		if (Session.get('token')) {
			config.headers['Token'] = `${Session.get('token')}`;
		}
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response: AxiosResponse<any, any>) => {
		//刷新token
		if (response.headers.token) {
			Session.set('token', response.headers.token);
		}
		// 对响应数据做点什么
		return Promise.resolve(response);
	},
	(error) => {
		// 对响应错误做点什么
		if (error.response.status == 401) {
			ElMessage.warning('用户过期或无权限！');
			Session.clear(); // 清除浏览器全部临时缓存
			window.location.href = '/'; // 去登录页
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误！');
		} else if (error.response?.status === 503) {
			ElMessage.error('服务不可用！');
		} else if (error.response?.status === 500) {
			ElMessage.error('服务端异常，请重试或咨询管理员！');
		} else if (error.response?.status === 400) {
			ElMessage.error('参数错误！');
		}
		return Promise.reject(error);
	}
);

// 导出 axios 实例

export default async <T = any>(config: AxiosRequestConfig) => {
	const res = await service(config);
	return (res instanceof Blob ? res : res.data) as RequestResult;
};
