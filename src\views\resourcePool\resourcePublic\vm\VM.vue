<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
        <el-button type="primary" plain @click="groupOperation('qd')" v-if="permisData.xunijikaiji">开机</el-button>
        <el-button type="primary" plain @click="groupOperation('gb')" v-if="permisData.xunijiguanji">关机</el-button>
        <el-button type="primary" plain @click="groupOperation('cq')" v-if="permisData.xunijichongqi">重启</el-button>
        <el-button type="danger" plain  @click="groupOperation('sc')" v-if="permisData.xunijishanchu">删除</el-button>
        <el-dropdown trigger="click" @command="groupOperation" v-if="permisData.xunijiqiangzhichongqi || permisData.xunijiguanbidianyuan || permisData.xunijizanting || permisData.xunijihuifu || permisData.xunijiwanquankelong || permisData.xunijilianjiekelong || permisData.xunijiqianyi">
          <el-button type="primary" style="margin-left: 10px">
            更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="qzcq" v-if="permisData.xunijiqiangzhichongqi">强制重启</el-dropdown-item>
              <el-dropdown-item command="gbdy" v-if="permisData.xunijiguanbidianyuan">关闭电源</el-dropdown-item>
              <el-dropdown-item command="zt" v-if="permisData.xunijizanting">暂停</el-dropdown-item>
              <el-dropdown-item command="hf" v-if="permisData.xunijihuifu">恢复</el-dropdown-item>
              <el-dropdown-item command="wqkl" v-if="permisData.xunijiwanquankelong">完全克隆</el-dropdown-item>
              <el-dropdown-item command="ljkl" v-if="permisData.xunijilianjiekelong">链接克隆</el-dropdown-item>
              <el-dropdown-item command="qy" v-if="permisData.xunijiqianyi">迁移</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div v-if="powerItem.sousuo">
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area" v-if="powerItem.liebiao">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
				<template #level="{ row }">
					<span class="status-warn">{{ row.levelstr }}</span>
				</template>
        <!-- 状态 -->
        <template #status="{ row }">
					<span :style="{color:vmStatus('color',row.status)}">{{ vmStatus('text',row.status) }}</span>
				</template>
				<!-- 操作 -->
        <template #operation="{ row }">
          <el-dropdown trigger="click" @command="operateItem($event,row)" v-if="permisData.xunijixiugai || permisData.xunijishanchu || permisData.xunijikongzhitai">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="kzt" v-if="permisData.xunijikongzhitai">控制台</el-dropdown-item>
                <el-dropdown-item command="bj" v-if="permisData.xunijixiugai">修改</el-dropdown-item>
                <el-dropdown-item command="sc" style="color:red" divided v-if="permisData.xunijishanchu">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <span v-else>-</span>
        </template>
			</my-table>
    </div>
    <VMgeneral  ref="generalRef" @returnOK="returnOK"/>
    <VMmigrate ref="migrateRef" @returnOK="returnOK"/>
    <VMedit  ref="editRef" @returnOK="returnOK"/>
    <VmDelete ref="deleteRef" @returnOK="returnOK"/>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search,ArrowDownBold } from '@element-plus/icons-vue'
import { tabsVmQuery } from '/@/api/ResourcePool/vm'; // 接口
import { vmColumns,vmStatus } from '/@/model/resource'; // 表列、正则
import { dayjs,ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const VMgeneral = defineAsyncComponent(() => import('../vmOperate/VMgeneral.vue'));
const VMmigrate = defineAsyncComponent(() => import('../vmOperate/VMmigrate.vue'));
const VMedit = defineAsyncComponent(() => import('../vmOperate/VMedit.vue'));
const VmDelete = defineAsyncComponent(() => import('../vmOperate/VmDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: vmColumns as Array<MyTableColumns>, // 表格表头配置
  tableSelect: [],
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  return new Promise(async(resolve)=>{
    let type = ''
    switch (props.treeItem.level) {
      case 1:
        type = 'pool'
      break;
      case 2:
        type = 'cluster'
      break;
      case 3:
        type = 'host'
      break;
    }
    tabsVmQuery({
      type: type, // 树节点
      _id: props.treeItem.id, // 树ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      }) 
    })
  })
};
const tableRef = ref();
// 刷新
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
const generalRef = ref(); // 通用操作
const migrateRef = ref(); // 迁移
// 表格群操作
const groupOperation = (item: string)=>{
  if(state.tableSelect.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    switch (item) {
      case "qd":
        generalRef.value.openDialog('开机','green',state.tableSelect);
      break;
      case "gb":
        generalRef.value.openDialog('关机','red',state.tableSelect);
      break;
      case "cq":
        generalRef.value.openDialog('重启','red',state.tableSelect);
      break;
      case "sc":
        deleteClick(state.tableSelect)
      break;
      case "qzcq":
        generalRef.value.openDialog('强制重启','red',state.tableSelect);
      break;
      case "gbdy":
        generalRef.value.openDialog('关闭电源','red',state.tableSelect);
      break;
      case "zt":
        generalRef.value.openDialog('暂停','red',state.tableSelect);
      break;
      case "hf":
        generalRef.value.openDialog('恢复','green',state.tableSelect);
      break;
      case "wqkl":
        generalRef.value.openDialog('完全克隆','green',state.tableSelect);
      break;
      case "ljkl":
        generalRef.value.openDialog('链接克隆','green',state.tableSelect);
      break;
      case "qy":
        migrateRef.value.openDialog(state.tableSelect);
      break;
      default:
        console.log('其他')
    }
  }
}
// 表操作列
const deleteRef = ref();
const editRef = ref();
const operateItem = (item: string,row: any)=>{
  switch (item) {
    case "kzt":
      // 构建带参数的URL
      const spiceUrl = `/spice-html5/spice.html?list=${props.treeItem.ip}&port=${row.spice_port}`
      window.open(spiceUrl, '_blank')
      console.log('控制台',spiceUrl)
    break;
    case "bj":
      editRef.value.openDialog(row);
    break;
    case "sc":
      deleteClick([row])
    break;
  }
}
// 删除操作
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    
    deleteRef.value.openDialog(arr,props.treeItem);
  }
}
const emit = defineEmits(['returnOK']);
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'refresh'){
    refresh()
    emit('returnOK', 'refresh');
  }
}
watch(
  ()=> props.treeItem,
  (val)=>{
    if(tableRef.value){
			refresh();
		}
  }
);
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:['xunijiliebiao','xunijisousuo']}).then((res:any)=>{
		powerItem.liebiao = res.data.xunijiliebiao;
		powerItem.sousuo = res.data.xunijisousuo;
    if (powerItem.liebiao) {
      setTimeout(() => {
        // refresh()
      }, 500);
    }
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>