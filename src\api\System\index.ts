import request from '/@/utils/request';
// 用户 查询
export const userQuery = () => {
	return request({
		url: '/user/v1/user/all',
		method: 'get',
	});
};
// 用户 添加
export const userNew = (data: object) => {
	return request({
		url: '/user/v1/user/add',
		method: 'post',
		data,
	});
};
// 用户 修改
export const userEdit = (data: object) => {
	return request({
		url: '/user/v1/user/update',
		method: 'put',
		data,
	});
};
// 用户 状态
export const userStatus = (data: object) => {
	return request({
		url: '/user/v1/user/status',
		method: 'put',
		data,
	});
};
// 用户 重置密码
export const userReset = (data: object) => {
	return request({
		url: '/user/v1/user/resetpassword',
		method: 'put',
		data,
	});
};
// 用户 删除
export const userDelete = (data: object) => {
	return request({
		url: '/user/v1/user/delete',
		method: 'delete',
		data,
	});
};
// 权限获取
export const powerTreeQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/userinfo',
		method: 'get',
		params: data,
	});
};
// 权限修改
export const powerTreeEdit = (data: object) => {
	return request({
		url: '/acapi/v1/auth/update',
		method: 'put',
		data,
	});
};
// 权限重置
export const powerTreeReset = (data: object) => {
	return request({
		url: '/acapi/v1/auth/default',
		method: 'put',
		data,
	});
};
// 权限可用性
export const powerUsabilityQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/module',
		method: 'post',
		data,
	});
};
// 权限可用性code码
export const powerCodeQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/check',
		method: 'post',
		data,
	});
};
// 权限查询
export const authorizeQuery = () => {
	return request({
		url: '/acapi/authentication/info',
		method: 'get',
	});
};

// 自动宕机迁移查询
export const automaticShutdownQuery = () => {
	return request({
		url: '/theapi/auto/evacuate',
		method: 'get',
	});
};
// 自动宕机迁移修改
export const automaticShutdownEdit = (data: object) => {
	return request({
		url: '/theapi/auto/evacuate',
		method: 'post',
		data,
	});
};
// 自动退出时间 查询
export const quitTimeQuery = () => {
	return request({
		url: '/theapi/session/out/time',
		method: 'get',
	});
};
// 自动退出时间 修改
export const quitTimeEdit = (data: object) => {
	return request({
		url: '/theapi/v1/session/out/time/update',
		method: 'put',
		data,
	});
};
// 防病毒配置 查询
export const antivirusConfigQuery = () => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'get',
	});
};
// 防病毒配置 修改
export const antivirusConfigEdit = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};

// 管理节点 查询
export const managementNodeQuery = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};
// 管理节点 扫描
export const managementNodeScan = () => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'get',
	});
};
// 管理节点 添加
export const managementNodeAdd = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};
// 管理节点 删除
export const managementNodeDelete = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'delete',
		data,
	});
};
// 集群查询分配
export const clusterAllocationQuery = (data: object) => {
	return request({
		url: '/theapi/v5/quota/clusters',
		method: 'post',
		data,
	});
};
// 集群分配
export const clusterAllocation = (data: object) => {
	return request({
		url: '/theapi/v5/user/assign/clusters',
		method: 'post',
		data,
	});
};
// 主机分配
export const hostAllocation = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};
// 虚拟机查询分配
export const vmAllocationQuery = (data: object) => {
	return request({
		url: '/theapi/v5/quota/vms',
		method: 'post',
		data,
	});
};
// 虚拟机分配
export const vmAllocation = (data: object) => {
	return request({
		url: '/theapi/v5/user/assign/vms',
		method: 'post',
		data,
	});
};
// 存储池查询
export const storageAllocationQuery = (data: object) => {
	return request({
		url: '/theapi/v5/quota/storage/pools',
		method: 'post',
		data,
	});
};
// 存储池分配
export const storageAllocation = (data: object) => {
	return request({
		url: '/theapi/v5/user/assign/storage_pools',
		method: 'post',
		data,
	});
};
// 配额查询
export const quotaQuery = (data: String) => {
	return request({
		url: '/theapi/v5/user/quota/' + data,
		method: 'get',
		data,
	});
};
// 配额分配
export const quotaModify = (data: object) => {
	return request({
		url: '/theapi/v5/user/assign/quota',
		method: 'post',
		data,
	});
};
// 配额最大
export const quotaMaxQuery = () => {
	return request({
		url: '/theapi/v5/quota/summary',
		method: 'get',
	});
};
