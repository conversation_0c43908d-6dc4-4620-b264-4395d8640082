<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="虚拟机迁移"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="物理机" prop="hostID">
        <el-select v-model="formItem.hostID" style="width: 100%">
          <el-option v-for="item in formItem.hostData" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { hostAllQuery } from '/@/api/Network'; // 接口
import { vmTransfer } from '/@/api/ResourcePool/vm'; // 接口


const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  hostID: '',
  hostData: [{ name: '', id: '' }],
  oldID: '',
  names: [''],
  ids: [''],
});
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (formItem.hostID === '') {
    ElMessage.error('请选择主机！');
    return false;
  }else{
    formItem.isShow = false;
    vmTransfer({
      names: formItem.names,
      ids: formItem.ids,
      new_host_id: formItem.hostID,
      migration_type: 'live'
    })
    .then(res => {
      if(res.code == 200) {
        ElMessage.success(res.msg)
        emit('returnOK', 'refresh');
      }else{
        ElMessage.error(res.msg)
      }
    })
  }
}
const hostQuery = () =>{
  let hostData: any[] = []
  hostAllQuery()
  .then(res => {
    res?.forEach((em:any) => {
      if(em.id != formItem.oldID) {
        hostData.push({
          name: em.name, 
          id: em.id
        })
      }
    });
    formItem.hostData = hostData
    formItem.hostID = hostData[0].id
  })
}
// 打开弹窗
const openDialog = async (arr: any) => {
  formItem.isShow = true;
	nextTick(() => {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formItem.names = names
    formItem.ids = ids
    hostQuery()
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>