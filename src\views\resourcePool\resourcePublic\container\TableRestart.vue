<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="重启容器"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="重启容器" prop="timeout">
        <el-input v-model="formItem.timeout" type="number" placeholder="指定秒数作为关机超时（默认：10）"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerOperate } from '/@/api/ResourcePool/container'; // 接口
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name: '',
  id: '',
  timeout: '',
});
const propTimeout = (rule:any, value:any, callback:any) => {
  if (value>0) {
    callback()
  } else {
    callback(new Error("请输入大于0的数字"))
  }
}
const rules = reactive<FormRules>({
  timeout: [
    { required: true, message: '必填项' },
    { validator: propTimeout, trigger: "blur" },
  ],
  
})

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        containerOperate({
          id: formItem.id,
          name: formItem.name,
          action: 'restart',
          timeout: formItem.timeout
        })
        .then(res => {
          ElMessage.success('重启容器操作已完成')
          emit('returnOK', 'refresh');
        })
        .catch(err => {
          ElMessage.error('重启容器操作已失败')
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		formItem.isShow = true;
    formItem.id = row.id
    formItem.name = row.name
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>