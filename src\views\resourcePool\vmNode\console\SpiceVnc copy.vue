<template>
  <div class="spice-iframe-container">
    <!-- 控制栏 -->
    <div class="control-bar">
      <div class="connection-info">
        <el-icon class="info-icon"><Monitor /></el-icon>
        <span class="info-text">SPICE HTML5控制台</span>
        <el-tag :type="getStatusType(state.status)" size="small">{{ state.status }}</el-tag>
      </div>
      <div class="control-buttons">
        <el-button size="small" @click="refreshIframe" :disabled="state.loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="openInNewWindow">
          <el-icon><FullScreen /></el-icon>
          新窗口
        </el-button>
      </div>
    </div>

    <!-- iframe容器 -->
    <div class="iframe-wrapper">
      <!-- 加载状态 -->
      <div v-if="state.loading" class="loading-overlay">
        <div class="loading-content">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <h3>正在加载SPICE HTML5客户端...</h3>
          <p>{{ state.loadingMessage }}</p>
        </div>
      </div>

      <!-- SPICE iframe -->
      <iframe
        ref="spiceIframeRef"
        :src="state.iframeSrc"
        frameborder="0"
        class="spice-iframe"
        @load="handleIframeLoad"
        @error="handleIframeError"
        :style="{ visibility: state.loading ? 'hidden' : 'visible' }"
      ></iframe>
    </div>

    <!-- 状态提示 -->
    <div v-if="state.showStatus" class="status-toast">
      <el-alert
        :title="state.statusMessage"
        :type="state.statusType"
        :closable="true"
        @close="state.showStatus = false"
        show-icon
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick, ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Monitor,
  Refresh,
  FullScreen,
  Loading
} from '@element-plus/icons-vue';

// 定义变量内容
const state = reactive({
  host: '',
  port: '',
  status: '未连接',
  loading: true,
  loadingMessage: '正在初始化SPICE客户端...',
  showStatus: false,
  statusMessage: '',
  statusType: 'info' as 'success' | 'warning' | 'info' | 'error',
  iframeSrc: '/spice-html5/spice_auto.html'
});

const spiceIframeRef = ref<HTMLIFrameElement>();
const emit = defineEmits(['returnOK']);

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已连接': return 'success';
    case '连接中': return 'warning';
    case '连接失败': return 'danger';
    default: return 'info';
  }
};

// 构建SPICE HTML5 URL
const buildSpiceUrl = (host: string, port: string) => {
  const baseUrl = '/spice-html5/spice_auto.html';
  const params = new URLSearchParams({
    host: host,
    port: port,
    password: '', // 可以根据需要添加密码
    encrypt: '0', // 是否加密
    title: `SPICE - ${host}:${port}`,
    // 其他SPICE参数
    resize: '1',
    clipboard: '1',
    autoconnect: '1'
  });

  return `${baseUrl}?${params.toString()}`;
};

// 刷新iframe
const refreshIframe = () => {
  if (spiceIframeRef.value) {
    state.loading = true;
    state.loadingMessage = '正在重新加载...';
    state.status = '连接中';

    // 重新设置src来刷新iframe
    const currentSrc = state.iframeSrc;
    state.iframeSrc = '';

    nextTick(() => {
      state.iframeSrc = currentSrc;
    });

    showStatus('正在刷新SPICE连接...', 'info');
  }
};

// 在新窗口打开
const openInNewWindow = () => {
  if (state.iframeSrc) {
    const newWindow = window.open(state.iframeSrc, '_blank', 'width=1024,height=768,resizable=yes,scrollbars=yes');
    if (newWindow) {
      showStatus('已在新窗口打开SPICE控制台', 'success');
    } else {
      showStatus('无法打开新窗口，请检查浏览器弹窗设置', 'warning');
    }
  }
};

// iframe加载完成
const handleIframeLoad = () => {
  console.log('SPICE iframe加载完成');
  state.loading = false;
  state.status = '已连接';
  showStatus('SPICE HTML5客户端加载成功', 'success');

  // 尝试与iframe通信
  try {
    const iframe = spiceIframeRef.value;
    if (iframe && iframe.contentWindow) {
      // 可以在这里向iframe发送消息
      iframe.contentWindow.postMessage({
        type: 'spice-config',
        host: state.host,
        port: state.port
      }, '*');
    }
  } catch (error) {
    console.log('iframe通信失败:', error);
  }
};

// iframe加载错误
const handleIframeError = () => {
  console.error('SPICE iframe加载失败');
  state.loading = false;
  state.status = '连接失败';
  showStatus('SPICE HTML5客户端加载失败，请检查文件是否存在', 'error');
};

// 显示状态信息
const showStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  state.statusMessage = message;
  state.statusType = type;
  state.showStatus = true;

  // 自动隐藏成功和信息提示
  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      state.showStatus = false;
    }, 3000);
  }
};

// 解析HTTP URL并提取主机和端口
const parseHttpUrl = (httpUrl: string) => {
  try {
    // 如果是完整的URL格式
    if (httpUrl.startsWith('http://') || httpUrl.startsWith('https://')) {
      const url = new URL(httpUrl);
      return {
        host: url.hostname,
        port: url.port || (url.protocol === 'https:' ? '443' : '80')
      };
    }

    // 如果是 host:port 格式
    const parts = httpUrl.split(':');
    if (parts.length >= 2) {
      return {
        host: parts[0].trim(),
        port: parts[1].trim()
      };
    }

    // 如果只有主机名，使用默认SPICE端口
    if (parts.length === 1 && parts[0].trim()) {
      return {
        host: parts[0].trim(),
        port: '5900' // SPICE默认端口
      };
    }

    throw new Error('无效的URL格式');
  } catch (error) {
    console.error('解析URL失败:', error);
    throw new Error('无效的连接地址格式');
  }
};

// 打开弹窗并设置连接信息
const openDialog = async (httpUrl: string) => {
  nextTick(() => {
    try {
      console.log('接收到连接地址:', httpUrl);

      // 解析连接地址
      const { host, port } = parseHttpUrl(httpUrl);

      state.host = host;
      state.port = port;
      state.status = '连接中';
      state.loading = true;
      state.loadingMessage = `正在连接到 ${host}:${port}...`;

      // 构建新的iframe URL
      state.iframeSrc = buildSpiceUrl(host, port);

      console.log(`SPICE iframe URL: ${state.iframeSrc}`);

      showStatus(`正在连接到 ${host}:${port}`, 'info');

    } catch (error) {
      console.error('解析连接地址失败:', error);
      state.status = '连接失败';
      state.loading = false;
      showStatus('无效的连接地址', 'error');
    }
  });
};

// 监听来自iframe的消息
window.addEventListener('message', (event) => {
  // 确保消息来自我们的iframe
  if (event.source === spiceIframeRef.value?.contentWindow) {
    console.log('收到iframe消息:', event.data);

    switch (event.data.type) {
      case 'spice-connected':
        state.status = '已连接';
        showStatus('SPICE连接成功', 'success');
        break;
      case 'spice-disconnected':
        state.status = '连接断开';
        showStatus('SPICE连接已断开', 'warning');
        break;
      case 'spice-error':
        state.status = '连接失败';
        showStatus('SPICE连接错误: ' + event.data.message, 'error');
        break;
    }
  }
});

// 暴露变量
defineExpose({
  openDialog,
  refreshIframe,
  openInNewWindow,
});
</script>
<style lang="scss" scoped>
.spice-iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .control-bar {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .connection-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .info-icon {
        font-size: 20px;
        color: #409eff;
      }

      .info-text {
        font-weight: 600;
        color: #303133;
        font-size: 16px;
      }
    }

    .control-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .iframe-wrapper {
    flex: 1;
    position: relative;
    background-color: #000;
    overflow: hidden;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #2c2c2c;
      z-index: 10;

      .loading-content {
        text-align: center;
        color: #fff;

        .loading-icon {
          font-size: 48px;
          color: #409eff;
          margin-bottom: 16px;
          animation: spin 2s linear infinite;
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: #fff;
        }

        p {
          margin: 0;
          color: #ccc;
          font-size: 14px;
        }
      }
    }

    .spice-iframe {
      width: 100%;
      height: 100%;
      border: none;
      background-color: #000;
    }
  }

  .status-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 300px;
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .spice-iframe-container {
    .control-bar {
      flex-direction: column;
      gap: 12px;
      padding: 8px 12px;

      .connection-info {
        justify-content: center;
      }

      .control-buttons {
        justify-content: center;
      }
    }
  }
}
</style>