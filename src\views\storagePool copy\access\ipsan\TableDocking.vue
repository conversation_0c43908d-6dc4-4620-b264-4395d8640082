<template>
	<el-dialog v-model="formItem.isShow" append-to-body class="dialog-500">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title">{{ formItem.title === 'new' ? '接入' : '修改' }} IPsan存储资源</span>
		</template>
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储设备名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
			<el-form-item label="存储 I P" prop="ip">
				<el-input v-model="formItem.ip" placeholder="请输入存储 IP" />
			</el-form-item>
			<el-form-item label="存储端口" prop="port">
				<el-input v-model="formItem.port" :min="1" type="number" placeholder="请输入端口" />
			</el-form-item>
			<el-form-item label="CHAP认证">
				<el-checkbox v-model="formItem.share" label="启用CHAP认证" />
			</el-form-item>
			<el-form-item label="用户" prop="user" v-if="formItem.share">
				<el-input v-model="formItem.user" placeholder="请输入用户" />
			</el-form-item>
			<el-form-item label="密码" prop="password" v-if="formItem.share">
				<el-input v-model="formItem.password" show-password placeholder="请输入密码" />
			</el-form-item>
			<el-form-item label="关联主机" v-if="formItem.title === 'new'" prop="selectNodes">
				<span @click="clickTree">已选 （ {{ formItem.selectNodes.length }} ）</span>
				<!-- <el-button @click="scanClick">扫描主机</el-button> -->
			</el-form-item>
		</el-form>
		<div class="ztree-publick" v-if="formItem.title === 'new'">
			<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { ipAccess, ipTableScan, ipTableEdit } from '/@/api/StoreManage'; // 接口
import { propName, propIP, propPort } from '/@/model/network'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	title: '',
	id: '',
	name: '',
	ip: '',
	port: '3260',
	share: false,
	user: '',
	password: '',
	selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '未获取到有效数据', pid: '0', status: '-' }],
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'blur' },
	],
	ip: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propIP, trigger: 'blur' },
	],
	port: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' },
	],
	user: [{ required: true, message: '必选项', trigger: 'blur' }],
	password: [{ required: true, message: '必选项', trigger: 'blur' }],
	selectNodes: [{ required: true, message: '必选项', trigger: 'blur' }],
});
const treeData = () => {
	if (!true) {
		formItem.zNodes = [
			{ id: '1', name: '资源节点', pid: '0', status: '-' },
			{ id: '2', name: '主机池1', pid: '1', status: '-' },
			{ id: '3', name: '集群1', pid: '2', status: '-' },
			{ id: '4', name: '集群2', pid: '2', status: '-' },
			{ id: '5', name: '主机1', pid: '3', status: '-' },
			{ id: '6', name: '主机3', pid: '4', status: '-' },
			// { id: '7', name: '主机6', pid: '3', status:'-',chkDisabled:true },
			// { id: '8', name: '主机1', pid: '3', status:'up' },
			// { id: '9', name: '主机2', pid: '3', status:'down' },
			// { id: '10', name: '主机3', pid: '4', status:'up' },
			// { id: '11', name: '主机4', pid: '4', status:'down' },
			// { id: '12', name: '主机6', pid: '4', status:'info' },
		];
	} else {
		resourceTreeQuery()
			.then((res) => {
				formItem.zNodes = res.data;
			})
			.catch((error) => {});
	}
};
const clickTree = () => {
	console.log('点击了');
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0', status: '-' },
		{ id: '2', name: '主机池1', pid: '1', status: '-' },
		{ id: '3', name: '集群1', pid: '2', status: '-' },
		{ id: '7', name: '集群2', pid: '2', status: '-' },
		{ id: '4', name: '主机1', pid: '3', status: 'down' },
		{ id: '5', name: '主机2', pid: '3', status: 'down' },
		{ id: '6', name: '主机3', pid: '3', status: 'down' },
		{ id: '8', name: '主机4', pid: '7', status: 'down' },
		{ id: '9', name: '主机6', pid: '7', status: 'down' },
	];
};
// 打开弹窗
const openDialog = async (type: string, row: any) => {
	formItem.isShow = true;
	formItem.title = type;
	formItem.id = row.id;
	nextTick(() => {
		if (type == 'new') {
			if (ruleFormRef.value) {
				// 确保 ruleFormRef 已初始化
				ruleFormRef.value.resetFields();
			}
			setTimeout(() => {
				formItem.type = 'IPsan-接入' + new Date();
				treeData();
			}, 200);
		} else {
			formItem.name = row.device_name;
			formItem.ip = row.ip_mgmt.split(':')[0];
			formItem.port = row.ip_mgmt.split(':')[1];
			if(row.username) {
				formItem.share = true;
				formItem.user = row.username;
				formItem.password = row.password;
			}else {
				formItem.share = false;
			}
			
		}
	});
};
const returnOK = (val: any) => {
	console.log('返回的树节点', val);
	formItem.selectNodes = val.filter((node: any) => node.level === 3);
};
const scanClick = () => {
	if (formItem.selectNodes.length > 0) {
		ElMessage.success('扫描主机');
		// ipTableScan({host:formItem.selectNodes}).then((res)=>{
		// 	ElMessage.success('扫描主机已完成');
		// });
		console.log('扫描主机', formItem.selectNodes);
	} else {
		ElMessage.error('请选择主机');
	}
};

const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				if (formItem.title === 'new') {
					ipAccess({
						name: formItem.name,
						ip: formItem.ip + ':' + formItem.port,
						share: formItem.share,
						user: formItem.user,
						password: formItem.password,
						host: formItem.selectNodes,
					}).then((res) => {
						if (res.msg == 'ok') {
							ElMessage.success('接入存储设备已完成');
							emit('returnOK', 'refresh');
						} else {
							ElMessage.error(res.msg);
						}
					});
				} else {
					ipTableEdit({
						id: formItem.id,
						name: formItem.name,
						ip: formItem.ip + ':' + formItem.port,
						share: formItem.share,
						user: formItem.user,
						password: formItem.password,
					}).then((res) => {
						if (res.msg == 'ok') {
							ElMessage.success('修改存储资源已完成');
							emit('returnOK', 'refresh');
						} else {
							ElMessage.error(res.msg);
						}
					});
				}
			}
		});
	}
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">
.ztree-publick {
	height: 200px;
}
</style>