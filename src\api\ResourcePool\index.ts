import request from '/@/utils/request';

// 节点 查询
export const resourceTreeQuery = () => {
	return request({
		url: '/theapi/v5/pool/tree',
		method: 'get',
	});
};
// 节点添加主机池
export const treeNewPool = (data: object) => {
	return request({
		url: '/theapi/v5/pool/create',
		method: 'post',
		data,
	});
};
// 节点查询主机池
export const treeQueryPool = (data: string) => {
	return request({
		url: '/theapi/v5/pool/detail/' + data,
		method: 'get',
	});
};
// 节点修改主机池
export const treeEditPool = (data: object) => {
	return request({
		url: '/theapi/v5/pool/name/update',
		method: 'post',
		data,
	});
};
// 节点删除主机池
export const treeDeletePool = (data: object) => {
	return request({
		url: '/theapi/v5/pool/delete',
		method: 'post',
		data,
	});
};
// 资源节点 概览
export const resourcesOverview = () => {
	return request({
		url: '/theapi/v5/resource/overview',
		method: 'get',
	});
};
// 主机池 概览
export const poolOverview = (data: string) => {
	return request({
		url: '/theapi/v5/pool/overview/' + data,
		method: 'get',
	});
};
// 集群 概览
export const colonyOverview = (data: string) => {
	return request({
		url: '/theapi/v5/cluster/overview/' + data,
		method: 'get',
	});
};
// 主机 概览
export const hostOverview = (data: string) => {
	return request({
		url: '/theapi/v5/host/overview/' + data,
		method: 'get',
	});
};
// 虚拟机 概览
export const vmOverview = (data: string) => {
	return request({
		url: '/theapi/v5/vm/overview/' + data,
		method: 'get',
	});
};
// 主机池 查询
export const hostPoolQuery = (data: object) => {
	return request({
		url: '/theapi/v5/pool/lists',
		method: 'post',
		data,
	});
};
// 集群 查询
export const colonyQuery = (data: object) => {
	return request({
		url: '/theapi/v5/cluster/lists',
		method: 'post',
		data,
	});
};
// 主机 查询
export const hostQuery = (data: object) => {
	return request({
		url: '/theapi/v5/host/lists',
		method: 'post',
		data,
	});
};
// 硬件设备 查询
export const hardwareQuery = (data: object) => {
	return request({
		url: '/theapi/***',
		method: 'post',
		data,
	});
};

// 节点添加集群
export const treeAddClony = (data: object) => {
	return request({
		url: '/theapi/v5/cluster/create',
		method: 'post',
		data,
	});
};
// 节点添加主机
export const treeAddHost = (data: object) => {
	return request({
		url: '/theapi/v5/host/create',
		method: 'post',
		data,
	});
};
// 节点查询集群
export const treeQueryClony = (data: string) => {
	return request({
		url: '/theapi/v5/cluster/detail/' + data,
		method: 'get',
	});
};
// 节点修改集群
export const treeEditClony = (data: object) => {
	return request({
		url: '/theapi/v5/cluster/name/update',
		method: 'post',
		data,
	});
};
// 节点删除集群
export const treeDeleteClony = (data: object) => {
	return request({
		url: '/theapi/v5/cluster/delete',
		method: 'post',
		data,
	});
};
// 节点查询主机
export const treeQueryHost = (data: string) => {
	return request({
		url: '/theapi/v5/host/detail/' + data,
		method: 'get',
	});
};
// 节点修改主机
export const treeEditHost = (data: object) => {
	return request({
		url: '/theapi/v5/host/name/update',
		method: 'post',
		data,
	});
};
// 节点删除主机
export const treeDeleteHost = (data: object) => {
	return request({
		url: '/theapi/v5/host/delete',
		method: 'post',
		data,
	});
};
// 同步硬件信息
export const synchronousHardware = (data: object) => {
	return request({
		url: '/theapi/v5/host/refresh/' + data,
		method: 'get',
	});
};
// 硬件设备清单 查询
export const deviceListQuery = (data: object) => {
	return request({
		url: '/theapi/v5/host/hardware/' + data,
		method: 'get',
	});
};

// 虚拟机 备份
export const vmBackupsQuery = (data: object) => {
	return request({
		url: '/theapi/v5/',
		method: 'post',
		data,
	});
};
// 虚拟机 快照查询
export const vmSnapshotQuery = (data: object) => {
	return request({
		url: '/theapi/v5/vm/snapshot/list',
		method: 'post',
		data,
	});
};
// 虚拟机 快照添加
export const vmSnapshotAdd = (data: object) => {
	return request({
		url: '/theapi/v5/vm/snapshot/add',
		method: 'post',
		data,
	});
};

// 虚拟机 运行日志
export const vmOperationLogQuery = (data: object) => {
	return request({
		url: '******',
		method: 'post',
		data,
	});
};
// 虚拟机 虚拟机迁移日志
export const vmMigrateLogQuery = (data: object) => {
	return request({
		url: '******',
		method: 'post',
		data,
	});
};
// 虚拟机 性能监控
export const vmPerformanceQuery = (data: object) => {
	return request({
		url: '******',
		method: 'post',
		data,
	});
};
// 虚拟机 告警
export const vmAlarmQuery = (data: object) => {
	return request({
		url: '******',
		method: 'post',
		data,
	});
};
// 虚拟机 任务
export const vmTaskQuery = (data: object) => {
	return request({
		url: '******',
		method: 'post',
		data,
	});
};
