<template>
  <div class="tree-test">
    <h2>树形组件测试 - 修正后的 default-checked-keys 功能</h2>
    <div class="test-info">
      <p>测试数据说明：</p>
      <ul>
        <li>集群1下有6个主机，其中前3个主机(主机1-3)的checked为true，后3个主机(主机4-6)的checked为false</li>
        <li>修正后的逻辑：只选中叶子节点(主机1、主机2、主机3)，让Element Plus自动处理父节点的半选状态</li>
        <li>预期效果：主机1-3被选中，集群1、主机池1、资源节点显示为半选状态</li>
      </ul>
    </div>
    
    <div class="tree-container">
      <el-tree
        ref="treeRef"
        :data="treeData"
        node-key="id"
        show-checkbox
        :props="defaultProps"
        :expand-on-click-node="false"
        default-expand-all
        :default-checked-keys="checkedKeys"
        @check="handleCheckChange"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <span class="node-label" :title="node.label">{{ node.label }}</span>
            <span class="node-status">(checked: {{ data.checked }})</span>
          </div>
        </template>
      </el-tree>
    </div>
    
    <div class="debug-info">
      <h3>调试信息：</h3>
      <p><strong>默认选中的节点ID：</strong> {{ checkedKeys }}</p>
      <p><strong>当前选中的节点ID：</strong> {{ currentCheckedKeys }}</p>
      <p><strong>当前半选的节点ID：</strong> {{ currentHalfCheckedKeys }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup name="TreeTest">
import { ref, onMounted } from 'vue';
import { TreeInstance } from 'element-plus';
import { convertFlatToTree } from '/@/utils/treeUtils';

const treeRef = ref<TreeInstance>();
const treeData = ref<any[]>([]);
const checkedKeys = ref<string[]>([]);
const currentCheckedKeys = ref<string[]>([]);
const currentHalfCheckedKeys = ref<string[]>([]);

const defaultProps = {
  children: 'children',
  label: 'module',
};

// 修正后的计算默认选中节点的函数
const calculateDefaultCheckedKeys = (nodes: any[]): string[] => {
  const keys: string[] = [];
  
  const traverse = (nodeList: any[]) => {
    nodeList.forEach(node => {
      if (node.checked === true) {
        // 如果是叶子节点，直接添加
        if (!node.children || node.children.length === 0) {
          keys.push(node.id);
        } else {
          // 如果有子节点，检查所有子节点是否都为true
          const allChildrenChecked = checkAllChildrenChecked(node);
          if (allChildrenChecked) {
            // 所有子节点都为true，只选中当前节点
            keys.push(node.id);
          } else {
            // 部分子节点为true，递归处理子节点
            traverse(node.children);
          }
        }
      } else {
        // 当前节点为false，但可能有子节点为true
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      }
    });
  };
  
  traverse(nodes);
  return keys;
};

// 检查节点的所有子节点（递归）是否都为true
const checkAllChildrenChecked = (node: any): boolean => {
  if (!node.children || node.children.length === 0) {
    return node.checked === true;
  }
  
  return node.children.every((child: any) => {
    if (child.children && child.children.length > 0) {
      return child.checked === true && checkAllChildrenChecked(child);
    } else {
      return child.checked === true;
    }
  });
};

// 处理节点勾选变化
const handleCheckChange = (data: any, checked: boolean, indeterminate: boolean) => {
  if (treeRef.value) {
    currentCheckedKeys.value = treeRef.value.getCheckedKeys() as string[];
    currentHalfCheckedKeys.value = treeRef.value.getHalfCheckedKeys() as string[];
  }
  
  console.log('当前操作节点:', data);
  console.log('选中状态:', checked);
  console.log('半选状态:', indeterminate);
  console.log('所有选中的节点ID:', currentCheckedKeys.value);
  console.log('所有半选的节点ID:', currentHalfCheckedKeys.value);
};

// 初始化数据
const initData = () => {
  let zNodes = [
    { id: '1', module: '资源节点', pid: '0', checked: true },
    { id: '2', module: '主机池1', pid: '1', checked: true },
    { id: '3', module: '集群1', pid: '2', checked: true },
    { id: '4', module: '主机1', pid: '3', checked: true },
    { id: '5', module: '主机2', pid: '3', checked: true },
    { id: '6', module: '主机3', pid: '3', checked: true },
    { id: '7', module: '主机4', pid: '3', checked: false },
    { id: '8', module: '主机5', pid: '3', checked: false },
    { id: '9', module: '主机6', pid: '3', checked: false },
  ];
  
  treeData.value = convertFlatToTree(zNodes, {
    idKey: 'id',
    pidKey: 'pid',
    childrenKey: 'children',
    rootValue: '0'
  });
  
  // 计算默认选中的节点
  checkedKeys.value = calculateDefaultCheckedKeys(treeData.value);
  
  console.log('源数据:', zNodes);
  console.log('重构之后数据:', treeData.value);
  console.log('默认选中的节点:', checkedKeys.value);
};

onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.tree-test {
  padding: 20px;
  
  .test-info {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
    }
  }
  
  .tree-container {
    border: 1px solid #dcdfe6;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .custom-tree-node {
    display: flex;
    align-items: center;
    
    .node-label {
      margin-right: 10px;
    }
    
    .node-status {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .debug-info {
    background: #f0f9ff;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #409eff;
    
    h3 {
      margin-top: 0;
      color: #409eff;
    }
    
    p {
      margin: 8px 0;
      font-family: monospace;
    }
  }
}
</style>
