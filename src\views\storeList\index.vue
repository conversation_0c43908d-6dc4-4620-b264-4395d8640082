<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
            <el-button type="primary" plain @click="newClick">对接存储</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
          </div>
          <div>
            <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 展开行内容 -->
            <template #expandContent="{ row }">
              <div>
                <el-table :data="row.listData" style="width: 50%">
                  <el-table-column prop="name" label="名称" />
                  <el-table-column prop="date" label="时间" />
                </el-table>
              </div>
            </template>
						<!-- 类型 -->
						<template #name="{ row }">
              <el-button type="success" link @click="nameClick(row)">
                {{ row.name }}
              </el-button>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
            	<el-button type="danger" @click="deleteClick([row])">删除</el-button>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
    <TableNew :newTime="state.newTime" @returnOK="returnOK"></TableNew>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { storeColumns } from '/@/model/storeManage'; // 表列、正则
import { storeQuery,storeDelete } from '/@/api/StoreManage/index'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

import { useRouter } from 'vue-router';
const router = useRouter();
// 定义变量内容
const state = reactive({
  columns: storeColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  newTime: '',
  deleteTime: '',

  tableData: [
    ],
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(true){
		return {
			data: [
        // {name: '本地存储',type: '本地存储',id: 'list0',listData: [{name: '测试: 本地存储',date: new Date().getTime()}] },
        { name: 'CEPH',type: 'CEPH',id: 'list1',listData: [] },
        { name: 'NFC',type: 'NFC',id: 'list2',listData: [{name: '测试: NFC1',date: new Date().getTime()},{name: '测试: NFC2',date: new Date().getTime()}] },
        { name: '其它',type: '其它',id: 'list3',listData: [] }
      ], // 数据
			total: 4, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    storeQuery({}).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 名称
const nameClick = (row:any)=>{
  router.push('/StoreManage/Local');
}
// 添加存储
const newClick = ()=>{
  state.newTime = ''+new Date()
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '存储/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    storeDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.msg != 'ok'){
        refresh()
        ElMessage.success('删除存储操作完成');
      }else {
        ElMessage.error('删除存储操作失败');
      }
    })
  }else {
    refresh()
  }
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
	width: calc(100%);
	height: calc(100%);
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>