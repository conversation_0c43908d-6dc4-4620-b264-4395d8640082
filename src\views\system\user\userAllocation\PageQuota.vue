<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="默认端口组" prop="cpu">
      <el-input v-model="formItem.cpu" :max="formItem.cpuMax" type="number" placeholder="有效值1-4096" />
		</el-form-item>
		<el-form-item label="内存" prop="mem">
      <el-input v-model="formItem.mem" :max="formItem.memMax" type="number" placeholder="有效值1-4096" />
		</el-form-item>
    <el-form-item label="磁盘容量" prop="disk">
      <el-input v-model="formItem.disk" :max="formItem.diskMax" type="number" placeholder="有效值1-4096" />
		</el-form-item>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
  zeroData: {
		type: Object,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['twoOK']);
const formItem = reactive({
  cpu: '1',
	cpuMax: 100,
  mem: '1',
	memMax: 100,
  disk: '1',
	diskMax: 100,
});
const propCPU = (rule:any, value:any, callback:any) => { 
  if (value>0&& value<formItem.cpuMax) {
    callback()
  } else {
    callback(new Error("请输入大于0的数字"))
  }
};
const rulesForm = reactive<FormRules>({
	cpu: [{ validator: propCPU, trigger: 'change' }],
	mem: [{ validator: propCPU, trigger: 'change' }],
	disk: [{ validator: propCPU, trigger: 'change' }],
	
});
watch(
	() => props.zeroData.card,
	(val) => {
		console.log('val', val);
	}
);
watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('twoOK', formItem);
				}
			});
		}
	}
);
</script>
<style lang="scss" scoped>
</style>