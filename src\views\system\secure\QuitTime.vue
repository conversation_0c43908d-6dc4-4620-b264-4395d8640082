<template>
	<el-card>
		<div>
			<el-input v-model="state.exitTime" type="number" style="width: 200px">
				<template #append>分钟</template>
			</el-input>
			<el-button type="warning" plain @click="changeSubmit" style="margin-left: 20px">更改提交</el-button>
		</div>
	</el-card>
</template>
<script setup lang="ts" name="QuitTime">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { quitTimeQuery, quitTimeEdit } from '/@/api/System'; // 接口
const state = reactive({
	exitTime: '30',
});
// 自动退出时间 查询
const getExitTime = () => {
	quitTimeQuery().then((res) => {
		state.exitTime = res.auto_exit_time;
	});
};
// 自动退出时间 更改
const changeSubmit = () => {
	let regex = /^(10|[1-9][0-9]+)$/;
	if (regex.test(state.exitTime)) {
		quitTimeEdit({ auto_exit_time: state.exitTime }).then((res) => {
			ElMessage.success('自动退出时间设置完成');
		});
	} else {
		ElMessage.error('请输入不小于10的整数时间');
	}
};
onMounted(() => {
	// getExitTime()
});
</script>