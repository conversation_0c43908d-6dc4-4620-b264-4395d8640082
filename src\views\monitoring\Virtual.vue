<template>
	<div class="monitoring-area layout-padding">
    <el-card>
			<div class="virtual-monitor-area">
				<div class="virtual-area" v-for="(item,index) in state.virtualData" :key="index">
					<!-- 虚拟机 基本数据 -->
					<div class="virtual-basic">
						<el-card>
							<div class="virtual-basic-title">
								<span>{{item.hostname}}（{{item.ip}}）</span>
          			<span>虚拟机总数量：<span class="virtual-title-count">{{item.vmCount}}</span></span>
							</div>
							<div class="virtual-basic-center">
								<img src="../../assets/images/vm-zt.png" alt="">
								<div class="virtual-center-type">
									<div class="center-type-area vm-run">
										<el-tooltip :disabled='item.openNumber==0'>
											<div class="vm-public-number">开机数量 <span>{{item.openNumber}}</span></div>
											<template #content>
												<el-table border :data="item.openData" style="width: 500px" height='200'>
													<el-table-column prop="name" label="名称" />
													<el-table-column prop="ip" label="IP" />
													<el-table-column prop="rely" label="代理" width='100'>
														<template #default="{row}">
															<el-tag :type="row.rely?'success':'info'">{{row.rely?'已安装':'未安装'}}</el-tag>
														</template>
													</el-table-column>
												</el-table>
											</template>
											<el-button>Top center</el-button>
										</el-tooltip>
									</div>
									<div class="center-type-area vm-fault">
										<el-tooltip :disabled='item.faultNumber==0'>
											<div class="vm-public-number">故障数量 <span>{{item.faultNumber}}</span></div>
											<template #content>
												<el-table border :data="item.faultData" style="width: 500px" height='200'>
													<el-table-column prop="name" label="名称" />
													<el-table-column prop="ip" label="IP" />
												</el-table>
											</template>
										</el-tooltip>
									</div>
									<div class="center-type-area vm-shutdown">
										<el-tooltip :disabled='item.closeNumber==0'>
											<div class="vm-public-number">关机数量 <span>{{item.closeNumber}}</span></div>
											<template #content>
												<el-table border :data="item.closeData" style="width: 500px" height='200'>
													<el-table-column prop="name" label="名称" />
													<el-table-column prop="ip" label="IP" />
												</el-table>
											</template>
										</el-tooltip>
										
									</div>
									<div class="center-type-area vm-alarm">
										<el-tooltip :disabled='item.alarmNumber==0'>
											<div class="vm-public-number">告警数量 <span>{{item.alarmNumber}}</span></div>
											<template #content>
												<el-table border :data="item.alarmData" style="width: 500px" height='200'>
													<el-table-column prop="name" label="名称" />
													<el-table-column prop="ip" label="IP" />
												</el-table>
											</template>
										</el-tooltip>
									</div>
								</div>
							</div>
						</el-card>
					</div>
					<!-- 虚拟机 图表数据 -->
					<div class="virtual-chart">
						<el-card>
							<div class="virtual-chart-area">
								<div class="radio-chart-select">
									<div class="chart-radio">
										<div class="radio-chart-piece" v-for="every in state.trendData" :key="every.lable" @click="radioClick(index,every.lable,item.time,item.hostname)">
											<span class="radio-title" :style="{color:item.trend==every.lable?'#121529':'#717379'}">{{every.title}}</span>
											<span class="radio-selected" :style="{background:item.trend==every.lable?'#fe6902':''}"></span>
										</div>
									</div>
									<el-select v-model="item.time" style="width:250px" @change="timeChange(index,item.trend,item.time,item.hostname)">
										<el-option label="最近1小时" :value="1" />
										<el-option label="最近24小时" :value="24" />
										<el-option label="最近1个月" :value="720" />
									</el-select>
								</div>
								<div class="virtual-chart-content">
									<div class="chart-no-datas" v-if="!item.chartShwo">
										<img src="../../assets/images/no-data.svg" >
									</div>
            			<EchartLine :chartData="item.chatdata" :time="item.time" v-if="item.chartShwo"></EchartLine>
								</div>
							</div>
						</el-card>
					</div>
				</div>
			</div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
	import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
	import { selectTime,listvmQuery } from '/@/model/monitoring'; // 表列、正则
	import { virtualBasicQuery,virtualCpuQuery,virtualMemQuery,virtualDiskQuery,virtualNetQuery } from '/@/api/Monitoring'; // 接口
	
	const EchartLine = defineAsyncComponent(() => import('./echartPublic/EchartLine.vue'));

	// 定义变量内容
	const state = reactive({
		allTital: 0,
		virtualData: [{
			hostname:"占无数据",
      ip:"-",
      vmCount: 0,
      openNumber: 0, // 正常
      openData: [],
      closeNumber: 0, // 关机
      closeData:[],
      alarmNumber: 0, // 告警
      alarmData:[],
      faultNumber: 0, // 故障
      faultData:[],
      trend: "cpu", // 默认显示图表
      time: 1, // 默认图表时间段
      chatdata:{},
			chartShwo: false,
		}],
		trendData: [
			{title:"CPU趋势",lable:"cpu"},
      {title:"内存趋势",lable:"memory"},
      {title:"磁盘趋势",lable:"disk"},
      {title:"网络趋势",lable:"network"},
		]
	});
	// 基本数据查询
	const basicData = ()=>{
		// virtualBasicQuery().then(res=>{
			let listNumber = 0
			let arr = new Array()
			// res.forEach((em:any,index:number)=> {
			listvmQuery.forEach((em:any,index:number)=> {
				listNumber += em.vm_count
				arr.push({
					hostname: em.hostname,
					ip: em.ip,
					vmCount: em.vm_count,
					openNumber: em.count_normal,
					openData: em.count_normal_list,
					closeNumber: em.count_shutoff,
					closeData: em.count_shutoff_list,
					alarmNumber: em.count_alarm,
					alarmData: em.count_alarm_list,
					faultNumber: em.count_fault,
					faultData: em.count_fault_list,
					trend: em.trend,
					time: em.time,
					chatdata: {},
					chartShwo: false,
				})
			});
			state.virtualData = arr
			state.allTital = listNumber
			state.virtualData.forEach((em:any,index:number)=>{
				radioClick(index,em.trend,em.time,em.hostname)
			})
		// })
	}
	
	// 时间选择
	const timeChange = (index:number,trend:string,time:number,name:string)=>{
		radioClick(index,trend,time,name)
	}
	// 趋势切换
	const radioClick = (index:number,trend:string,time:number,name:string)=>{
		state.virtualData[index].trend = trend
		let list = {
			start: selectTime(time),
			end: selectTime(0),
			hostname: name
		}
		switch (trend) {
			case 'cpu':
				cpuQuery(index,list)
				break;
			case 'memory':
				memQuery(index,list)
				break;
			case 'disk':
				diskQuery(index,list)
				break;
			case 'network':
				netQuery(index,list)
				break;
		}
	}
		// 获取CPU图表数据
	const cpuQuery = (index:number,list:any)=>{
		state.virtualData[index].chatdata = {
			unit: 'B',
			data: [
				{title: '物理机1上行',list:[123456,734562,1,132456,574632]},
				{title: '物理机1下行',list:[null,102904,837491,122243,109]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.virtualData[index].chartShwo = true
		// virtualCpuQuery(list).then(res=>{
		// 	if(res.data?.length>0) {
		// 		state.virtualData[index].chatdata = res
		// 		state.virtualData[index].chartShwo = true
		// 	}else {
		// 		state.virtualData[index].chartShwo = false
		// 	}
		// })
	}
	// 获取内存图表数据
	const memQuery = (index:number,list:any)=>{
		state.virtualData[index].chatdata = {
			unit: 'B',
			data: [
				{title: '物理机1',list:[10,20,5,30,1]},
      	{title: '物理机2',list:[50,10,30,1,70]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.virtualData[index].chartShwo = true
		// virtualMemQuery(list).then(res=>{
		// 	if(res.data?.length>0) {
		// 		state.virtualData[index].chatdata = res
		// 		state.virtualData[index].chartShwo = true
		// 	}else {
		// 		state.virtualData[index].chartShwo = false
		// 	}
		// })
	}
	// 获取磁盘图表数据
	const diskQuery = (index:number,list:any)=>{
		state.virtualData[index].chatdata = {
			unit: 'B',
			data: [
				{title: '物理机1',list:[1,2,3,4,5]},
      	{title: '物理机2',list:[5,4,3,2,1]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.virtualData[index].chartShwo = true
		// virtualDiskQuery(list).then(res=>{
		// 	if(res.data?.length>0) {
		// 		state.virtualData[index].chatdata = res
		// 		state.virtualData[index].chartShwo = true
		// 	}else {
		// 		state.virtualData[index].chartShwo = false
		// 	}
		// })
	}
	// 获取网络图表数据
	const netQuery = (index:number,list:any)=>{
		state.virtualData[index].chatdata = {
			unit: 'B',
			data: [
				{title: '物理机1',list:[3,7,51,34,12]},
      	{title: '物理机2',list:[-5,-1,-33,-10,-27]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.virtualData[index].chartShwo = true
		// virtualNetQuery(list).then(res=>{
		// 	if(res.data?.length>0) {
		// 		state.virtualData[index].chatdata = res
		// 		state.virtualData[index].chartShwo = true
		// 	}else {
		// 		state.virtualData[index].chartShwo = false
		// 	}
		// })
	}


	// 页面加载时
	onMounted(() => {
		basicData()
	});
</script>

<style scoped lang="scss">
	.monitoring-area {
		padding-top: 0 !important;
    width: 100%;
    height: 100%;
		.virtual-monitor-area {
			width: 100%;
			height: 100%;
			overflow: auto;
			.virtual-area {
				width: 100%;
				height: 350px;
				margin-bottom: 15px;
				display: flex;
    		justify-content: space-between;
				.virtual-basic {
					width: 600px;
					height: 100%;
					.virtual-basic-title {
						height: 20px;
						font-size: 15px;
						display: flex;
    				justify-content: space-between;
						span:nth-child(1) {
							font-weight: 800;
						}
						.virtual-title-count {
							width: 40px;
							color: #fe6902;
							display: inline-block;
							text-align: right;
						}
					}
					.virtual-basic-center {
						width: 100%;
						height: calc(100% - 20px);
						display: flex;
    				align-items: center;
					}
					img {
						width: 150px;
						height: 150px;
					}
					.virtual-center-type {
						width: 100%;
						height: 100%;
						padding: 35px 0;
						display: flex;
						flex-wrap: wrap;
						justify-content: space-around;
						align-items: center;
						.center-type-area {
							width: 200px;
							height: 60px;
							.vm-public-number {
								width: 100%;
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: flex-end;
								span {
									display: inline-block;
									font-weight: 600;
									width: 80px;
									text-align: left;
									padding-left: 20px;
								}
							}
						}
					}
				}
				.virtual-chart {
					width: calc(100% - 610px);
					height: 100%;
					.virtual-chart-area {
						width: 100%;
						height: 100%;
						.radio-chart-select {
							display: flex;
							justify-content: space-between;
							height: 50px;
							.chart-radio {
								display: flex;
								justify-content: space-between;
								align-items: center;
								font-size: 15px;
								.radio-chart-piece {
									display: flex;
									align-content: space-between;
									flex-direction: column;
									align-items: center;
									margin-right: 26px;
									font-weight: 800;
									cursor: pointer;
									.radio-selected {
										width: 10px;
										height: 6px;
										border-radius: 3px;
									}
								}
							}
						}
						.virtual-chart-content {
							width:100%;
        			height:calc(100% - 50px);
							.chart-no-datas {
								width: 100%;
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: center;
								img {
									height: 70%;
								}
							}
						}
					}
				}
			}
		}
	}
  .el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
.vm-run {
	background-image: url("../../assets/images/vm-run.png");
  background-size: 100% 100%;
}
.vm-fault {
	background-image: url("../../assets/images/vm-fault.png");
  background-size: 100% 100%;
}
.vm-shutdown {
	background-image: url("../../assets/images/vm-shutdown.png");
  background-size: 100% 100%;
}
.vm-alarm {
	background-image: url("../../assets/images/vm-alarm.png");
  background-size: 100% 100%;
}
</style>
