<template>
    <div class="layout-parent">
        <div class="route-content">
            <router-view></router-view>
        </div>
    </div>
</template>

<script setup lang="ts" name="layoutParentView">
import { reactive, onUnmounted, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import mittBus from '/@/utils/mitt';

// 定义类型
interface ParentViewState {
    refreshRouterViewKey: string;
    iframeRefreshKey: string;
    keepAliveNameList: string[];
    iframeList: any[];
    routeList: any[];
}

// 定义变量内容
const route = useRoute();
const state = reactive<ParentViewState>({
    refreshRouterViewKey: '', // 非 iframe tagsview 右键菜单刷新时
    iframeRefreshKey: '', // iframe tagsview 右键菜单刷新时
    keepAliveNameList: [],
    iframeList: [],
    routeList: [],
});

// 获取子路由列表
const getChildRoutes = () => {
    const parentRouteRecord = route.matched[route.matched.length - 2];
    if (parentRouteRecord && parentRouteRecord.children) {
        state.routeList = parentRouteRecord.children;
    }
};

// 页面加载时
onMounted(() => {
    getChildRoutes();
});

// 页面卸载时
onUnmounted(() => {
    mittBus.off('onTagsViewRefreshRouterView', () => { });
});

// 监听路由变化，防止 tagsView 多标签时，切换动画消失
watch(
    () => route.fullPath,
    () => {
        state.refreshRouterViewKey = decodeURI(route.fullPath);
        getChildRoutes();
    },
    {
        immediate: true,
    }
);
</script>
<style lang="scss" scoped>
.layout-parent {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.route-nav-container {
    margin-bottom: 20px;
}

.route-content {
    width: 100%;
    position: relative;
    flex: 1;
    overflow: hidden;
}
</style>