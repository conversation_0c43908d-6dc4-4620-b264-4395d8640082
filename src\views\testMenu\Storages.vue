<template>
	<div class="content-wrap">
		<div class="content-main">
			<div class="search-group">
				<el-form :inline="true" :model="formInline" ref="formRef">
					<el-row>
						<el-col :span="18">
							<el-row :gutter="10">
								<el-col :span="8">
									<el-form-item label="任务状态" prop="step" class="search-row-span">
										<el-select v-model="formInline.step" placeholder="请选择任务状态" clearable class="search-row-span">
											<el-option label="完成" :value="0" />
											<el-option label="创建中" :value="1" />
											<el-option label="介质准备" :value="4" />
											<el-option label="分配迁移器" :value="6" />
											<el-option label="执行中" :value="8" />
											<el-option label="失败" :value="9" />
											<el-option label="取消" :value="10" />
											<el-option label="手动取消" :value="11" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="任务类型" prop="taskType" class="search-row-span">
										<el-select v-model="formInline.taskType" placeholder="请选择任务类型" clearable class="search-row-span">
											<el-option label="数据归档" :value="1" />
											<el-option label="客户端归档(D2D)" :value="13" />
											<el-option label="客户端归档(D2T)" :value="14" />
											<el-option label="数据恢复" :value="6" />
											<el-option label="数据清理" :value="7" />
											<el-option label="磁带复制" :value="8" />
											<el-option label="磁带格式化" :value="9" />
											<el-option label="ORACLE备份" :value="10" />
											<el-option label="ORACLE挂载" :value="11" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="任务号" prop="taskId" class="search-row-span">
										<el-input v-model="formInline.taskId" placeholder="请输入任务号" clearable />
									</el-form-item>
								</el-col>
							</el-row>
							<el-row v-show="formFolded" :gutter="10">
								<el-col :span="8">
									<el-form-item label="开始时间" prop="startTime" class="search-row-span">
										<el-date-picker
											class="search-row-span"
											v-model="formInline.startTime"
											type="datetime"
											placeholder="请选择任务开始时间"
											format="YYYY-MM-DD hh:mm:ss"
											value-format="YYYY-MM-DD hh:mm:ss"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="结束时间" prop="deadline" class="search-row-span">
										<el-date-picker
											class="search-row-span"
											v-model="formInline.deadline"
											type="datetime"
											placeholder="请选择任务结束时间"
											format="YYYY-MM-DD hh:mm:ss"
											value-format="YYYY-MM-DD hh:mm:ss"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="所属策略" prop="policyId" class="search-row-span">
										<el-select v-model="formInline.policyId" placeholder="请选择所属策略" clearable class="search-row-span">
											<el-option v-for="policy of policyList" :label="policy" :value="policy" />
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
						<el-col :span="5" :offset="1">
							<el-form-item>
								<el-button type="primary" v-antiShake="onSubmit" :icon="Search">查询</el-button>
								<el-button v-antiShake="resetForm" :icon="RefreshLeft">重置</el-button>
								<el-button v-if="!formFolded" link :icon="ArrowDownBold" @click="formFolded = true">展开</el-button>
								<el-button v-else link :icon="ArrowUpBold" @click="formFolded = false">折叠</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="task-table">
				<el-card shadow="hover">
					<div class="btn-group">
						<el-button :icon="Promotion" type="success" @click="nowExecuteASelect">加急</el-button>
						<el-button :icon="RefreshLeft" type="primary" @click="reTryTasks">重试</el-button>
						<el-button :icon="DocumentDelete" @click="cancelTaskSelect">取消</el-button>
						<el-button :icon="DeleteFilled" @click="deleteTaskSelect" type="danger">删除</el-button>
						<el-button :icon="Refresh" type="primary" @click="onSubmit">刷新</el-button>
						<el-checkbox v-model="autoRefresh" @change="changeRefresh" label="自动刷新" size="large" style="float: right" />
					</div>
					<div class="table-content">
						<MyTable
							ref="myTable"
							:pagination="{ show: true }"
							rowKey="snapshotId"
							:searchParams="formInline"
							:hideTitleBar="false"
							@selectionChange="selectionChange"
							:request="getList"
							:columns="columns"
						>
							<template #object_id="{ row }">
								<template v-if="row.taskType == 1">
									<!-- <el-button @click="handleToObject(row.objectId)" link>{{ row.objectId }}</el-button> -->
								</template>
							</template>
							<template #page-tool="{ row }">
								<!-- <el-button :icon="Promotion" title="任务加急" type="primary" @click="nowExecute(row.taskId)"></el-button>
									<el-button :icon="RefreshLeft" title="重试任务" type="primary" @click="reTryTask(row.taskId)"></el-button> -->
								<el-button :icon="More" title="任务详情" @click="getTaskDetail()"></el-button>
							</template>
						</MyTable>
					</div>
				</el-card>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup name="StoragePool">
	import { reactive, ref, onMounted, defineAsyncComponent, watch, nextTick, onUnmounted, inject } from 'vue';
	import {
		Plus,
		Minus,
		Promotion,
		Edit,
		DeleteFilled,
		DocumentDelete,
		ArrowDown,
		More,
		ArrowDownBold,
		ArrowUpBold,
		RefreshLeft,
		Refresh,
		Search,
	} from '@element-plus/icons-vue';
	import { ElMessage } from 'element-plus';
	import { deleteKeysforObject } from '/@/utils/formatUtil';
	import { useRouter } from 'vue-router';
	const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
	const props = defineProps({
		jobId: {
			type: Number,
			default: undefined,
		},
	});
	const columns = reactive<Array<MyTableColumns>>([
		{ type: 'selection', wrap: true },
		{ label: '序号', width: '60px', type: 'index', align: 'center', sortable: true, wrap: true },
		{ label: '任务号', prop: 'taskId', align: 'left', sortable: true, wrap: false },
		{ label: '任务名称', prop: 'taskName', align: 'left', wrap: false },
		{ label: '任务类型', prop: 'taskTypeName', align: 'left', wrap: true },
		{ label: '任务状态', prop: 'stepName', align: 'left', wrap: true },
		// { label: "归档速度（MB/s）", prop: "avgSpeed",align: "center", wrap: true },
		{ label: '执行主机', prop: 'agentMoverHost', align: 'left', wrap: true },
		{ label: '磁带条码', prop: 'mediaCode', align: 'left', wrap: true },
		{ label: '对象ID', prop: 'objectId', tdSlot: 'object_id', align: 'left', wrap: false },
		{ label: '开始时间', prop: 'execStartTime', sortable: true, align: 'left', wrap: true },
		{ label: '结束时间', prop: 'execEndTime', sortable: true, align: 'left', wrap: true },
		{ label: '操作', fixed: 'right', width: 190, tdSlot: 'page-tool', align: 'left' },
	]);
	const formFolded = ref(false);
	const formInline = reactive({
		step: undefined,
		taskType: undefined,
		startTime: undefined,
		deadline: undefined,
		policyId: undefined,
		groupId: undefined,
		userId: undefined,
		jobId: '',
		taskId: '',
		objectId: '',
	});
	const selectList = ref<EmptyArrayType>([]);
	const selectionChange = (rowList: EmptyArrayType) => {
		// 勾选方法
		selectList.value = rowList;
	};
	const formRef = ref();
	const myTable = ref();
	const onSubmit = () => {
		nextTick(() => {
			formFolded.value = false;
			myTable.value.handleSearch();
		});
	};
	const resetForm = () => {
		formRef.value.resetFields();
		onSubmit();
	};
	const getList = async (params: EmptyObjectType, page: EmptyObjectType) => {
		return {
			data: [], // 数据
			total: 0 // 总数
		}
	};
	const cancelTaskSelect = async () => {};
	const taskDetailVisable = ref(false);
	const taskDetialInfo = reactive({});
	const getTaskDetail = () => {};
	const closeDialog = () => {
		taskDetailVisable.value = false;
		deleteKeysforObject(taskDetialInfo);
	};
	onMounted(() => {
		initAllPolicys();
	});
	onUnmounted(() => {
		if (timer.value) {
			clearInterval(timer.value);
		}
	});
	const timer = ref('');
	const autoRefresh = ref(true);
	const changeRefresh = () => {};
	const policyList = reactive([]);
	const groupList = reactive([]);
	const userList = reactive([]);
	const initAllPolicys = async () => {};
	const reTryTasks = () => {};
	const nowExecuteASelect = () => {};
	const deleteTaskSelect = () => {};
	// 跳转路由
	const router = useRouter();
	// 全局变量，注入对象号，用于跳转对象管理
	const tempData = inject('tempData');
</script>
<style lang="scss" scoped>
	.content-wrap {
		width: calc(100%);
		height: calc(100%);
		padding: 0 20px 20px 20px;
		.content-main {
			width: calc(100%);
			height: calc(100%);
			display: flex;
			flex-direction: column;
		}
		.search-group {
			min-height: 55px;
			.search-row-span {
				width: 100%;
				:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
					width: 100%;
				}
			}
		}
		.task-table {
			// height: calc(100% - 55px);
			flex: 1;
			overflow-y: auto;
			.el-card {
				height: calc(100%);
				position: relative;
				:deep(.el-card__header) {
					border: none;
				}
				.card-header {
					font-weight: bold;
					font-family: '黑体';
					font-size: 16px;
				}
				:deep(.el-card__body) {
					height: calc(100%);
					position: relative;
				}
				.btn-group {
					height: 40px;
				}
				.table-content {
					height: calc(100% - 40px);
					padding-top: 10px;
					position: relative;
				}
				.error-info {
					color: #ff4d44;
				}
				.warn-info {
					color: #fd7819;
				}
				.success-info {
					color: #31d731;
				}
			}
		}
	}
</style>
