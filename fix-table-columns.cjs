const fs = require('fs');
const path = require('path');

// 递归查找所有 .vue 文件
function findVueFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findVueFiles(filePath));
      }
    } else {
      const ext = path.extname(file);
      if (ext === '.vue') {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的表格列类型问题
function fixTableColumnsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复表格列定义中的类型问题
    // 将 unknown 类型的属性修复为正确的类型
    
    // 修复 prop 属性
    if (content.includes('prop?: unknown')) {
      content = content.replace(/prop\?\s*:\s*unknown/g, 'prop?: string');
      modified = true;
    }
    
    // 修复 align 属性
    if (content.includes('align?: unknown')) {
      content = content.replace(/align\?\s*:\s*unknown/g, 'align?: "left" | "center" | "right"');
      modified = true;
    }
    
    // 修复 width 属性
    if (content.includes('width?: unknown')) {
      content = content.replace(/width\?\s*:\s*unknown/g, 'width?: string | number');
      modified = true;
    }
    
    // 修复 tdSlot 属性
    if (content.includes('tdSlot?: unknown')) {
      content = content.replace(/tdSlot\?\s*:\s*unknown/g, 'tdSlot?: string');
      modified = true;
    }
    
    // 修复 sortable 属性
    if (content.includes('sortable?: unknown')) {
      content = content.replace(/sortable\?\s*:\s*unknown/g, 'sortable?: boolean');
      modified = true;
    }
    
    // 修复 wrap 属性
    if (content.includes('wrap?: unknown')) {
      content = content.replace(/wrap\?\s*:\s*unknown/g, 'wrap?: boolean');
      modified = true;
    }
    
    // 修复 type 属性
    if (content.includes('type?: unknown')) {
      content = content.replace(/type\?\s*:\s*unknown/g, 'type?: string');
      modified = true;
    }
    
    // 修复 label 属性
    if (content.includes('label?: unknown')) {
      content = content.replace(/label\?\s*:\s*unknown/g, 'label?: string');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed table columns in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding Vue files to fix table columns...');
  
  const files = findVueFiles(srcDir);
  console.log(`Found ${files.length} Vue files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixTableColumnsInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed table columns in ${fixedCount} files`);
}

main();
