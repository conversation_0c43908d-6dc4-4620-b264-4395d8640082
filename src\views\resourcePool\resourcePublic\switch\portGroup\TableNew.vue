<template>
  <el-dialog
    v-model="formItem.isShow"
    title="添加端口组"
    append-to-body
    class="dialog-500"
  >
    <div class="form-switch-area">
      <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="端口组名称" prop="portName">
          <el-input v-model="formItem.portName"  placeholder="请输入端口组名称"/>
        </el-form-item>
        <el-form-item label="端口类型">
          <el-select v-model="formItem.portType" style="width: 100%">
            <el-option label="普通" value="pt" />
            <el-option label="中链" value="zl" />
          </el-select>
        </el-form-item>
        <el-form-item label="VLAN" prop="vlan">
          <el-input v-model="formItem.vlan" type="number" placeholder="请输入端口组VLAN"/>
        </el-form-item>
        <el-form-item label="网络优先级">
          <el-select v-model="formItem.priority" style="width: 100%">
            <el-option label="低" value="low" />
            <el-option label="中" value="medun" />
            <el-option label="高" value="high" />
          </el-select>
        </el-form-item>
        <el-form-item label="平均带宽">
          <div class="kbps-input">
            <el-input v-model="formItem.rpjdk" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">入口(KBps)</span>
              </template>
            </el-input>
            <el-input v-model="formItem.cpjdk" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">出口(KBps)</span>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="突发缓冲">
          <div class="kbps-input">
            <el-input v-model="formItem.rtfhc" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">入口(KBps)</span>
              </template>
            </el-input>
            <el-input v-model="formItem.ctfhc" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">出口(KBps)</span>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="广播包接收限速">
          <div class="kbps-input">
            <el-input v-model="formItem.ipjsxs" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">I P(KBps)</span>
              </template>
            </el-input>
            <el-input v-model="formItem.arpjsxs" :min="1" type="number">
              <template #append>
                <span class="kbps-unit">ARP(KBps)</span>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="拦截DHCP">
          <el-checkbox v-model="formItem.dhcp" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { portGroupNew } from '/@/api/Network'; // 接口
import { propName,propNumber } from '/@/model/resource'; // 表列、正则

const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  switchForm: {
    id: '',
  },
  portName: '',
  portType: 'pt',
  vlan: '',
  priority: 'low',
  rpjdk: '',
  cpjdk: '',
  rtfhc: '',
  ctfhc: '',
  ipjsxs: '',
  arpjsxs: '',
  dhcp: false,
});

const rules = reactive<FormRules>({
  portName: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" }  
  ],
  vlan: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propNumber, trigger: "change" }
  ],
})
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        portGroupNew({
          switch_id: formItem.switchForm.id,
          name: formItem.portName,
          vlan_id: formItem.vlan,
        })
        .then((res:any) => {
          emit('returnOK', 'refresh');
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
    formItem.switchForm = row
    formItem.portType = 'pt'
    formItem.priority = 'low'
    formItem.rpjdk = ''
    formItem.cpjdk = ''
    formItem.rtfhc = ''
    formItem.ctfhc = ''
    formItem.ipjsxs = ''
    formItem.arpjsxs = ''
    formItem.dhcp = false
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  .form-switch-area {
    width: 100%;
    overflow: auto;
    .port-forn-area {
      padding: 0 0 20px 0;
      text-align: center;
    }
    .kbps-input {
      display: flex;
      .kbps-unit {
        width: 60px;
      }
    }
  }
</style>