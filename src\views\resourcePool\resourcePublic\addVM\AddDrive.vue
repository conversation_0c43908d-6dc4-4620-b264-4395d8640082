<template>
<div>
	<el-form ref="formRef" label-position="left" label-width="150" :model="formModel">
		<div v-for="(item, index) in driveData" :key="'drive' + index">
			<el-form-item>
				<template #label>
					<div class="vm-new-label">
						<span>{{ index == 0 ? '光驱' : '光驱' + index }}</span>
					</div>
				</template>
				<el-input v-model="item.driveName" disabled>
					<template #append>
						<!-- <el-button :icon="Search" @click="searchClick1(index)"/> -->
						<el-button :icon="Search" @click="searchClick(index)"/>
						<el-button v-if="item.remove" @click="removeDrive(index)" :icon="Delete" style="margin-left: 20px" />
					</template>
				</el-input>
			</el-form-item>
			<!-- <el-form-item label="网卡多队列" v-show="item.driveName !== ''">
				<el-input v-model="item.drivestorage" disabled />
			</el-form-item>-->
		</div>
	</el-form>
	<SelectDrive ref="driveRef" @drive-return="driveReturn" />
	<SelectCD ref="existingRef" @existing-return="existingReturn" />
</div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
const SelectDrive = defineAsyncComponent(() => import('./SelectDrive.vue'));
const SelectCD = defineAsyncComponent(() => import('./SelectCD.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
	driveAdd: {
		type: Number,
		required: true,
	},
	driveTime: {
		type: String,
		required: true,
	},
});
const formData = reactive({
	driveIndex: 0,
})
const formModel = ref({}); // 可以动态填充你的表单数据
const formRef = ref<FormInstance>();
const emit = defineEmits(['driveOK']);
// 硬件信息-光驱
let driveData = reactive([
  {
    remove: false,
    driveName: '',
		poolID: '',
		driveID: '',
		driveType: '',
		drivePath: '',
    drivestorage: '',
  }
])

// 选择光驱
const driveRef = ref();
const searchClick1 = (item: any) => {
	driveRef.value.driveDialog()
	formData.driveIndex = item
};
// 选择光驱返回
const driveReturn = (item: any) => {
	driveData[formData.driveIndex].driveName = item.tableName
	driveData[formData.driveIndex].drivestorage = item.tableID
};
const existingRef = ref();
const searchClick = (item: any) => {
	existingRef.value.existingDialog(props.treeItem)
	formData.driveIndex = item
};
// 现有磁盘选择选中
const existingReturn = (item: any)=>{
  driveData[formData.driveIndex].poolID = item.poolID
  driveData[formData.driveIndex].driveID = item.diskID
  driveData[formData.driveIndex].driveName = item.tableName
  driveData[formData.driveIndex].driveType = item.tableType
  driveData[formData.driveIndex].drivePath = item.tablePath

  // driveData[formData.driveIndex].drivestorage = item.tablePath
}
watch(
  ()=> props.driveTime,
  async (val)=>{
		emit('driveOK', driveData); 
    // if (formRef.value) {
    //   try {
    //     // 执行表单验证
    //     await formRef.value.validate();
    //     // 如果验证通过，触发事件
    //     emit('driveOK', driveData);  // 取消注释以触发事件
    //   } catch (error) {
    //     // 如果验证失败，标记失败项并输出错误
    //     driveData.forEach((item, index) => {
    //       // 这里可以增加对特定字段的验证，标记 `typeShow` 为 `true`
		// 			emit('driveOK', false)
    //       item.typeShow = true;
    //     });
    //   }
    // }
  } 
);
watch(
	() => props.driveAdd,
	(val) => {
		driveData.push({
      remove: true,
      driveName: '',
			poolID: '',
			driveID: '',
			driveType: '',
			drivePath: '',
      drivestorage: '',
    })
	}
);
// 删除光驱
const removeDrive = (item: any)=>{
  driveData.splice(item,1)
}

</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>