<template>
<div>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    class="dialog-1000"
  >
    <template #header="{ close, titleId, titleClass }">
      <span class="el-dialog__title">{{props.tableRow.name}} 存储池-磁盘</span>
    </template>
    <div class="dialog-area">
      <div class="tabs-btn-area">
        <div>
          <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
          <el-button type="primary" plain @click="newClick" v-if="powerItem.tianjia">添加磁盘</el-button>
          <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
        </div>
        <div v-if="powerItem.sousuo">
          <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
            <template #append>
              <el-button :icon="Search" @click="refresh"></el-button>
            </template>
          </el-input>
        </div>
      </div>
      <div class="table-area" v-if="powerItem.liebiao">
        <my-table
          ref="tableRef"
          :pagination="state.pagination"
          :columns="state.columns"
          :request="getTableData"
          @selectionChange='selectChange'
        >
          <!-- 分配容量 -->
          <template #capacity="{ row }">
            <span>{{ capacityConversion(row.capacity) }}</span>
          </template>
          <!-- 实际使用量 -->
          <template #allocation="{ row }">
            <span>{{ capacityConversion(row.allocation) }}</span>
          </template>
          <!-- 操作 -->
          <template #operation="{ row }">
            <el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.xiugai || powerItem.shanchu">
              <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
                  <el-dropdown-item command="sc" style="color:red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <span v-else>-</span>
          </template>
        </my-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">关闭</el-button>
        <!-- <el-button type="primary" @click="confirm">确认</el-button> -->
      </div>
    </template>
  </el-dialog>
  <DiskNew :newTime="state.newTime" :tableRow="props.tableRow" @returnOK="returnOK"></DiskNew>
  <DiskEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></DiskEdit>
	<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
</div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { DiskQuery,diskDelete } from '/@/api/ResourcePool/storage'; // 接口
import { diskColumns } from '/@/model/storage'; // 表列、正则
import { capacityConversion } from '/@/model/resource'; // 表列、正则
import { Search,ArrowDownBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const DiskNew = defineAsyncComponent(() => import('./DiskNew.vue'));
const DiskEdit = defineAsyncComponent(() => import('./DiskEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));


const props = defineProps({
  diskTime: {
    type: String,
    required: true
  },
  tableRow: {
    type: Object,
    required: true
  }
});
const state = reactive({
  isShow: false,
  columns: diskColumns,
  tableSelect: [],
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  newTime: '',
  tableRow: {},
  editTime: '',
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType,page: EmptyObjectType)=>{
  state.tableSelect = []
  return new Promise(async(resolve)=>{
    DiskQuery({
      pool_id: props.tableRow.id, // 存储池ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
}
const tableRef = ref();
// 刷新
const refresh = ()=>{
  
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// const emit = defineEmits(['existingReturn']);
const confirm =()=>{
  state.isShow= false
  // emit('existingReturn', {});
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  state.tableRow = row
  switch (item) {
    case 'bj':
      state.editTime = ''+new Date()
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 添加磁盘
const newClick = ()=>{
  state.newTime = ''+new Date()
}
// 表格选中变化
const selectChange = (row: never)=>{
  state.tableSelect = row
}
// 删除磁盘
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '磁盘/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    diskDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.code == 200){
        refresh()
        ElMessage.success(res.msg);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    refresh()
  }
}
watch(
  ()=> props.diskTime,
  (val)=>{
    state.isShow = true;
    setTimeout(() => {
      refresh()
    }, 100);
  }
);
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'cipanliebiao',
    'cipansousuo',
    'tianjiacipan',
    'shanchucipan',
    'xiugaicipan',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.cipanliebiao;
		powerItem.sousuo = res.data.cipansousuo;
		powerItem.tianjia = res.data.tianjiacipan;
		powerItem.shanchu = res.data.shanchucipan;
		powerItem.xiugai = res.data.xiugaicipan;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
  .dialog-area {
    height: 650px;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .table-area {
      position: relative;
      height: calc(100% - 50px);
      width: 100%;
    }
  }
</style>