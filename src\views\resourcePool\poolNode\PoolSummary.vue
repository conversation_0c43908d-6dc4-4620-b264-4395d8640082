<template>
	<div class="resource-pool-container">
		<div class="summary-pool-area">
			<div class="machine-area">
        <div class="colony-area">
          <img src="../../../assets/resource/jq.png" alt="" />
          <!-- <p style="cursor: pointer" @click="getData">集群 <span>{{ state.colony }}</span></p> -->
          <p>集群 <span>{{ state.colony }}</span></p>
        </div>
				<div class="physics-vm-area">
					<div class="physics-area">
            <div class="general-area">
              <img src="../../../assets/resource/wlj.jpg" alt="" />
              <p>物理机 <span>{{ state.physical }}</span></p>
            </div>
            <div class="status-area">
              <p style="width:80%">物理机状态：</p>
              <p><el-icon color="#81b337"><Monitor /></el-icon>正常 <span>{{state.physicalON}}</span></p>
              <p><el-icon color="#215476"><Monitor /></el-icon>异常 <span>{{state.physicalAbnormal}}</span></p>
              <p><el-icon color="#bd3124"><Monitor /></el-icon>关机 <span>{{state.physicalOFF}}</span></p>
            </div>
          </div>
					<div class="physics-area">
            <div class="general-area">
              <img src="../../../assets/resource/xnj.jpg" alt="" />
              <p>虚拟机 <span>{{ state.vm }}</span></p>
            </div>
            <div class="status-area">
              <p style="width:80%">虚拟机状态：</p>
              <p><el-icon color="#81b337"><Monitor /></el-icon>正常 <span>{{state.vmON}}</span></p>
              <p><el-icon color="#215476"><Monitor /></el-icon>异常 <span>{{state.vmAbnormal}}</span></p>
              <p><el-icon color="#bd3124"><Monitor /></el-icon>关机 <span>{{state.vmOFF}}</span></p>
            </div>
          </div>
				</div>
			</div>
			<div class="capacity-area">
        <div class="capacity-item">
          <img src="../../../assets/resource/cpu.jpg" alt="" />
          <div class="item-list">
            <p><span>CPU颗数: </span><span>{{state.cpu}}</span></p>
            <p><span>逻辑CPU核数: </span><span>{{state.logicCPU}}</span></p>
            <p><span>CPU频率: </span><span>{{hzConversion(state.cpuFrequency)}}</span></p>
          </div>
        </div>
        <div class="capacity-item">
          <img src="../../../assets/resource/jq.png" alt="" />
          <div class="item-list">
            <p><span>内存总量: </span><span>{{capacityConversion(state.memTota)}}</span></p>
            <p><span>已分配内存: </span><span>{{capacityConversion(state.memUsed)}}</span></p>
          </div>
        </div>
        <div class="capacity-item">
          <img src="../../../assets/resource/cc.jpg" alt="" />
          <div class="item-list">
            <p><span>存储总量: </span><span>{{capacityConversion(state.stoTota)}}</span></p>
            <p><span>已分配存储: </span><span>{{capacityConversion(state.stoUsed)}}</span></p>
          </div>
        </div>
      </div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search,Monitor } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { poolOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion,hzConversion } from '/@/model/resource'; // 表格 正则
const props = defineProps({
	treeItem: {
		type: Object,
		required: true,
	},
	acive: {
		type: String,
		required: true,
	},
});

const state = reactive({
	colony: '0',
	physical: '0',
  physicalON: '0',
  physicalAbnormal: '0',
  physicalOFF: '0',
	vm: '0',
  vmON: '0',
  vmAbnormal: '0',
  vmOFF: '0',
  cpu: '0',
  logicCPU: '0',
  cpuFrequency: '0',
  memTota: '0',
  memUsed: '0',
  stoTota: '0',
  stoUsed: '0',
});
// 概览数据
const getData = ()=>{
  poolOverview(props.treeItem.id).then(res=>{
    state.colony = res.data.cluster_count+' 个'
    state.physical = res.data.host_count+' 台'
    state.physicalON = res.data.host_up_count+' 台'
    state.physicalAbnormal =  res.data.host_error_count+' 台'
    state.physicalOFF =  res.data.host_down_count+' 台'
    state.vm = res.data.vm_count+' 台'
    state.vmON = res.data.vm_up_count +' 台'
    state.vmAbnormal = res.data.vm_error_count +' 台'
    state.vmOFF =  res.data.vm_down_count +' 台'
    state.cpu = res.data.cpu_all_count+' 颗'
    state.logicCPU = res.data.cpu_allocation_count+' 核'
    state.cpuFrequency = res.data.cpu_all_hz
    state.memTota = res.data.mem_all_count
    state.memUsed = res.data.mem_use_count
    state.stoTota = res.data.disk_all_count
    state.stoUsed = res.data.disk_use_count
  })
}
onMounted(() => {
	getData()
});
</script>
<style lang="scss" scoped>
.resource-pool-container {
  font-size: 22px;
	width: 1370px;
	height: 740px;
	.summary-pool-area {
		width: calc(100%);
		height: calc(100%);
		display: flex;
		justify-content: space-between;
		.machine-area {
			width: 670px;
			height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .colony-area {
        width: 670px;
        height: 220px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        img {
          margin-right: 50px;
        }
        span {
          font-weight: 800;
          font-size: 30px;
        }
      }
      .physics-vm-area {
        width: 670px;
        display: flex;
        justify-content: space-between;
        .physics-area {
          width: 320px;
          height: 490px;
          border: 1px solid var(--el-card-border-color);
          border-radius: var(--el-card-border-radius);
          box-shadow: var(--el-box-shadow-light);
          .status-area {
            width: 320px;
            height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-evenly;
            p {
              display: flex;
              align-items: center;
              span {
                display: inline-block;
                width: 80px;
                text-align: right;
              }
            }
          }
        }
      }
		}
		.capacity-area {
			width: 670px;
			height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .capacity-item {
        width: 670px;
        height: 220px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .item-list {
          width: 300px;
          p {
            span:first-of-type {
              display: inline-block;
              width: 160px;
            }
          }
        }
      }
		}
	}
}
.general-area {
  width:320px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    // margin-right: 30px;
  }
  span {
    font-weight: 800;
    font-size: 30px;
  }
}
img {
  width: 120px;
  height: 120px;
}

.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>