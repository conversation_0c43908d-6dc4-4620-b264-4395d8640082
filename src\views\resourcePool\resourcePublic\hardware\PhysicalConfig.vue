<template>
	<el-dialog v-model="dialogVisible" append-to-body :title="`配置网卡 - ${network?.name}`" class="dialog-500" @close="handleClose">
		<el-form :model="form" label-width="100px">
			<el-form-item label="IP 地址">
				<el-input v-model="form.ip" placeholder="请输入IP地址" />
			</el-form-item>
			<el-form-item label="子网掩码">
				<el-input v-model="form.mask" placeholder="请输入子网掩码" />
			</el-form-item>
			<el-form-item label="网关">
				<el-input v-model="form.gateway" placeholder="请输入网关地址" />
			</el-form-item>
			<el-form-item label="DNS">
				<el-input v-model="form.dns" placeholder="请输入DNS地址" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSave">保存</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
const props = defineProps({
	show: Boolean,
	network: Object,
});
const emit = defineEmits(['update:show', 'save']);
const dialogVisible = ref(false);
const form = ref({
	ip: '',
	mask: '',
	gateway: '',
	dns: '',
});
watch(
	() => props.show,
	(newVal) => {
		dialogVisible.value = newVal;
	}
);
watch(
	() => props.network,
	(newVal) => {
		if (newVal) {
			form.value = {
				ip: newVal.ip || '*************',
				mask: '*************',
				gateway: '***********',
				dns: '*******',
			};
		}
	},
	{ immediate: true }
);
const handleClose = () => {
	emit('update:show', false);
};
const handleSave = () => {
	emit('save', form.value);
	handleClose();
};
</script>