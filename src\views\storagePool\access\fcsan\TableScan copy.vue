<template>
	<el-dialog v-model="formItem.isShow" title="扫描FC-SAN存储资源" append-to-body class="dialog-500">
		<h3>勾选要扫描的主机</h3>
		<span>( 不选默认全部扫描 )</span>
		<div class="dialog-tree-body">
			<el-input v-model="formItem.search" class="search-input" placeholder="搜索主机" :prefix-icon="Search" />
			<el-tree ref="treeRef" class="filter-tree" :data="formItem.data" show-checkbox :props="defaultProps" default-expand-all node-key="id" highlight-current :filter-node-method="filterNode" />
		</div>
		<template #footer>
			<div class="dialog-footer">
				<!-- <el-button @click="formItem.isShow = false">取消</el-button> -->
				<el-button @click="getCheckedNodes">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules, ElTree } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network'; // 表列、正则
const treeRef = ref<InstanceType<typeof ElTree>>();
const formItem = reactive({
	isShow: false,
	search: '',
	data: [
		{
			id: 1,
			label: '组1',
			children: [
				{
					id: 4,
					label: '组1二级1',
					children: [
						{
							id: 9,
							label: '组1二级1三级1',
						},
						{
							id: 10,
							label: '组1二级1三级2',
						},
					],
				},
        {
					id: 11,
					label: '组1二级2',
					children: [
						{
							id: 12,
							label: '组1二级2三级1',
						},
						{
							id: 13,
							label: '组1二级2三级2',
						},
					],
				},
			],
		},
		{
			id: 2,
			label: '组2',
			children: [
				{
					id: 5,
					label: '组2二级1',
				},
				{
					id: 6,
					label: '组2二级2',
				},
			],
		},
	],
});
const defaultProps = {
	children: 'children',
	label: 'label',
};
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}
const getCheckedNodes = () => {
  console.log(treeRef.value!.getCheckedNodes(false, false))
}
watch(
  ()=> formItem.search,
  (val)=>{
    treeRef.value!.filter(val)
  }
);
// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
		formItem.search = '';
	});
};

const emit = defineEmits(['returnOK']);
const confirm = () => {};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">
h3 {
	display: inline-block;
	margin-bottom: 10px;
}
.dialog-tree-body {
	width: 100%;
	height: 400px;
	padding: 10px;
	border: 1px solid #ccc;
	.search-input {
		margin-bottom: 10px;
	}
	.filter-tree {
		width: 100%;
		height: 370px;
	}
}
</style>