<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
          </div>
          <div>
            <!-- 搜索位置 -->
          </div>
        </div>
        <div class="tabs-table-area">
          <div class="tabs-img-area">
            <img :src="state.imgUrl" alt="描述文字" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { ElMessage } from 'element-plus';

// 定义变量内容
const state = reactive({
  imgUrl: 'http://************/root/wlb/zx.jpg',
});

// 刷新
const refresh = ()=>{
  
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
  padding-top: 0 !important;
	width: calc(100%);
	height: calc(100%);
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
      .tabs-img-area {
        width: 400px;
        height: 340px;
        border: 1px solid #f5b35d;
      }
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>