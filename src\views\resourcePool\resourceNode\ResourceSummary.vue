<template>
  <div class="resource-pool-container">
    <div class="pool-container pool-public">
      <!-- <p style="cursor: pointer" @click="getData">主机池: {{ state.pool }}</p> -->
      <p>主机池: {{ state.pool }}</p>
    </div>
    <div class="cloiny-container pool-public">
      <p>集群: {{ state.cloiny }}</p>
    </div>
    <div class="host-container pool-public">
      <p>物理机: {{ state.host }}</p>
    </div>
    <div class="other-container">
      <div class="vm-container">
        <p>虚拟机: {{ state.vm }}</p>
      </div>
      <div class="cpu-container">
        <p>CPU使用率: {{ state.cpuRate }}</p>
        <p>总频率: {{ hzConversion(state.cpuTota) }}</p>
        <p>已使用: {{ hzConversion(state.cpuUsed) }}</p>
      </div>
      <div class="mem-container">
        <p>内存使用率: {{ state.memRate }}</p>
        <p>总量: {{ capacityConversion(state.memTota) }}</p>
        <p>已使用: {{ capacityConversion(state.memUsed) }}</p>
      </div>
      <div class="stor-container">
        <p>存储使用率: {{ state.stoRate }}</p>
        <p>总量: {{ capacityConversion(state.stoTota) }}</p>
        <p>已使用: {{ capacityConversion(state.stoUsed) }}</p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { resourcesOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion,hzConversion } from '/@/model/resource'; // 表格 正则
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  pool: '0',
  cloiny: '0',
  host: '0',
  vm: '0',
  cpuRate: '0',
  cpuTota: '0', // 频率
  cpuUsed: '0',
  memRate: '0', // 率
  memTota: '0', // 总
  memUsed: '0', // 已用
  stoRate: '0',
  stoTota: '0',
  stoUsed: '0',
});
// 概览数据
const getData = ()=>{
  resourcesOverview().then(res=>{
    state.pool = res.data.pool_count+' 个'
    state.cloiny = res.data.cluster_count+' 个'
    state.host = res.data.host_count+' 台'
    state.vm = res.data.vm_count+' 台'
    state.cpuRate = (res.data.cpu_use_hz/res.data.cpu_all_hz*100).toFixed(1)+' %'
    state.cpuTota = res.data.cpu_all_hz
    state.cpuUsed = res.data.cpu_use_hz
    state.memRate = (res.data.mem_use_count/res.data.mem_all_count*100).toFixed(1)+' %'
    state.memTota = res.data.mem_all_count
    state.memUsed = res.data.mem_use_count
    // parseFloat((res.data.mem_use_count/res.data.mem_all_count*100).toFixed(1))
    state.stoRate = (res.data.disk_use_count/res.data.disk_all_count*100).toFixed(1)+' %'
    state.stoTota = res.data.disk_all_count
    state.stoUsed = res.data.disk_use_count
  })
}
onMounted(() => {
  getData()
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: 740px;
    background-image: url('/@/assets/resource/root1.png');
    background-size: 1393px 740px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 20px;
    text-align: center;
    .pool-public {
      padding-top: 190px;
      height: 300px;
    }
    .pool-container {
      width: 270px; 
    }
    .cloiny-container {
      width: 300px;
    }
    .host-container {
      width: 270px;
    }
    .other-container {
      height: 100%;
      width: calc(100% - 843px);
      text-align: left;
      .vm-container {
        padding-left: 320px;
        height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .cpu-container {
        padding-left: 320px;
        height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .mem-container {
        padding-left: 320px;
        height: 190px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .stor-container {
        padding-left: 320px;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
    }
  }
</style>