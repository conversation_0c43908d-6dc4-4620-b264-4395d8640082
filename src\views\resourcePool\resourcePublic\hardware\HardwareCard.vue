<template>
	<el-card class="hardware-card" :body-style="{ padding: '10px' }">
		<template #header>
			<div class="card-header">
				<span class="header-title">
					<el-icon class="icon"><component :is="icon" /></el-icon>
					{{ title }}
				</span>
			</div>
		</template>
		<div class="hardware-list">
			<div v-for="(item, index) in items" :key="index" class="hardware-item">
				<div class="item-content">
					<div class="item-info">
						<!-- 物理网卡 -->
						<span class="item-name">{{ item.name }}</span>
						<span v-if="item.ip" class="item-specs"><span>IP地址</span> {{ item.ip == '' ? '-' : item.ip }}</span>
						<span v-if="item.mac" class="item-specs"><span>MAC地址</span> {{ item.mac }}</span>
						<span v-if="item.speed" class="item-specs"><span>速度</span> {{ item.speed }}</span>
						<span v-if="item.mtu" class="item-specs"><span>最大传输单元</span> {{ item.mtu }}</span>
						<!-- 本地磁盘 -->
						<span v-if="item.model" class="item-specs"><span>型号</span> {{ item.model }}</span>
						<span v-if="item.size" class="item-specs"><span>容量</span>{{ capacityConversion(item.size) }}</span>
						<!-- USB -->
						<span v-if="item.device" class="item-specs"><span>设备</span> {{ item.device }}</span>
						<span v-if="item.description" class="item-specs"><span>描述</span> {{ item.description }}</span>
						<!-- CPU -->
						<span v-if="item.cores" class="item-specs"><span>核心数</span> {{ item.cores }}</span>
						<span v-if="item.threads" class="item-specs"><span>线程数</span> {{ item.threads }}</span>
						<span v-if="item.pciPath" class="item-specs"><span>PCI路径</span> {{ item.pciPath }}</span>
						<span v-if="item.frequency" class="item-specs"><span>频率</span> {{ item.frequency }}</span>
						<!-- 内存 -->
						<span v-if="item.memSize" class="item-specs"><span>容量</span> {{ capacityConversion(item.memSize) }}</span>
						<!-- GPU -->
						<span v-if="item.deviceID" class="item-specs"><span>设备ID</span> {{ item.deviceID }}</span>
						<span v-if="item.gpuModel" class="item-specs"><span>型号</span> {{ item.gpuModel }}</span>
						<!-- HBA -->

						<!-- PCI设备、内存 - 类型 -->
						<span v-if="item.type" class="item-specs"><span>类型</span> {{ item.type }}</span>
						<!-- 物理网卡、本地磁盘、内存、GPU - 厂商 -->
						<span v-if="item.vendor" class="item-specs"><span>厂商</span> {{ item.vendor }}</span>
					</div>
					<div class="item-actions" v-if="showConfig&&item.name!=='暂无数据'">
						<el-button type="primary" :icon="Setting" circle size="small" @click="$emit('configure', item)" />
					</div>
				</div>
				<div class="item-status">
					<el-tag v-if="item.duplex" :type="item.duplex === 'Full' ? 'success' : 'info'" size="small">
						{{ item.duplex === 'Full' ? '全双工' : '半双工' }}
					</el-tag>
					<el-tag v-if="item.linkStatus" :type="item.linkStatus === 'up' ? 'success' : 'info'" size="small">
						{{ item.linkStatus === 'up' ? '已连接' : '未连接' }}
					</el-tag>
					<el-tag v-if="item.activityStatus" :type="item.activityStatus === 'up' ? 'success' : 'info'" size="small">
						{{ item.activityStatus === 'up' ? '活动' : '未活动' }}
					</el-tag>
					<el-tag v-if="item.status" :type="item.status === 'active'|| item.status === 'connected' ? 'success' : 'info'" size="small">
						{{ item.status === 'active'|| item.status === 'connected' ? '可用' : '不可用' }}
					</el-tag>
				</div>
			</div>
		</div>
	</el-card>
</template>
<script setup lang="ts">
import { capacityConversion } from '/@/model/resource'; // 表列、正则
import { Setting } from '@element-plus/icons-vue';

interface HardwareItem {
	name?: string;
	ip?: string;
	mac?: string;
	speed?: string;
	mtu?: string;
	model?: string;
	size?: number;
	device?: string;
	description?: string;
	cores?: number;
	threads?: number;
	pciPath?: string;
	frequency?: string;
	memSize?: number;
	deviceID?: string;
	gpuModel?: string;
	type?: string;
	vendor?: string;
	duplex?: string;
	linkStatus?: string;
	activityStatus?: string;
	status?: string;
}

defineProps<{
	title?: string;
	icon?: string;
	items?: HardwareItem[];
	showConfig?: boolean;
}>();
defineEmits(['configure']);
</script>
<style lang="scss" scoped>
.hardware-card {
	height: 100%;
	width: 100%;
	margin-bottom: 10px;
	.card-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.header-title {
			display: flex;
			align-items: center;
			gap: 8px;
			font-size: 14px;
			font-weight: 500;
			.icon {
				font-size: 16px;
				color: var(--el-color-primary);
			}
		}
	}
	.hardware-list {
		height: 260px;
		display: flex;
		flex-direction: column;
		overflow: auto;
		gap: 10px;
		.hardware-item {
			padding: 12px;
			background: var(--el-bg-color-page);
			border-radius: 4px;
			.item-content {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				.item-info {
					display: flex;
					flex-direction: column;
					gap: 4px;
				}
				.item-name {
					font-size: 14px;
					font-weight: 500;
				}

				.item-specs {
					font-size: 12px;
					color: var(--el-text-color-secondary);
					padding: 5px 0;
					span:first-of-type {
						display: inline-block;
						margin-right: 10px;
						width: 80px;
					}
				}
			}
			.item-status {
				margin-top: 8px;
			}
		}
	}
}
</style>