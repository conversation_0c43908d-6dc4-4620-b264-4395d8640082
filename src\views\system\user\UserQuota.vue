<template>
	<el-dialog v-model="formItem.isShow" title="配额" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="CPU" prop="cpu">
        <el-input v-model="formItem.cpu" type="number" :max="formItem.cpuMax">
					<template #append>
            <span>核</span>
					</template>
				</el-input>
      </el-form-item>
      <el-form-item label="内存" prop="mem">
        <el-input v-model="formItem.mem" type="number" :max="formItem.memMax">
					<template #append>
						<el-select v-model="formItem.MEMunit" style="width: 80px" @change="handleMemUnitChange">
							<el-option label="B" value="B" />
							<el-option label="KB" value="KB" />
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
      </el-form-item>
      <el-form-item label="磁盘容量" prop="disk">
        <el-input v-model="formItem.disk" type="number" :max="formItem.diskMax">
					<template #append>
						<el-select v-model="formItem.DISKunit" style="width: 80px" @change="handleDiskUnitChange">
							<el-option label="B" value="B" />
							<el-option label="KB" value="KB" />
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
      </el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { quotaQuery,quotaModify,quotaMaxQuery } from '/@/api/System'; // 接口

const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	userName: '',
	userID: '',
	cpu: '1',
	cpuMax: 100,
  mem: '1',
	memMax: 100,
  MEMunit: 'KB',
  disk: '1',
	diskMax: 100,
  DISKunit: 'KB',
});
const propCPU = (rule:any, value:any, callback:any) => { 
  if (value>=0&& value<=formItem.cpuMax) {
    callback()
  } else {
    callback(new Error("请输入大于等于0小于等于"+formItem.cpuMax+"的数字"))
  }
};
const propMEM = (rule:any, value:any, callback:any) => {
  const currentBytes = unitValueToBytes(parseFloat(value) || 0, formItem.MEMunit);
  const maxMemBytes = formItem.memMax;
  const maxMemDisplay = bytesToUnitValue(maxMemBytes);

  if (value >= 0 && currentBytes <= maxMemBytes) {
    callback()
  } else {
    callback(new Error(`请输入大于等于0小于等于${maxMemDisplay.value}${maxMemDisplay.unit}的数字`))
  }
};
const propDisk = (rule:any, value:any, callback:any) => {
  const currentBytes = unitValueToBytes(parseFloat(value) || 0, formItem.DISKunit);
  const maxDiskBytes = formItem.diskMax;
  const maxDiskDisplay = bytesToUnitValue(maxDiskBytes);

  if (value >= 0 && currentBytes <= maxDiskBytes) {
    callback()
  } else {
    callback(new Error(`请输入大于等于0小于等于${maxDiskDisplay.value}${maxDiskDisplay.unit}的数字`))
  }
};
const rules = reactive<FormRules>({
	cpu: [{ validator: propCPU, trigger: 'change' }],
	mem: [{ validator: propMEM, trigger: 'change' }],
	disk: [{ validator: propDisk, trigger: 'change' }],
});
// 将字节转换为带单位的数值和单位
const bytesToUnitValue = (bytes: number) => {
  if (!bytes || bytes === 0) return { value: 0, unit: 'B' };

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let unitIndex = 0;
  let size = bytes;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return {
    value: Math.floor(size * 100) / 100,
    unit: units[unitIndex]
  };
};

// 将带单位的数值转换为字节
const unitValueToBytes = (value: number, unit: string) => {
  const multipliers: { [key: string]: number } = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024,
    'TB': 1024 * 1024 * 1024 * 1024
  };

  return Math.floor(value * (multipliers[unit] || 1));
};

// 处理内存单位变化
const handleMemUnitChange = () => {
  nextTick(() => {
    if (ruleFormRef.value) {
      ruleFormRef.value.validateField('mem');
    }
  });
};

// 处理磁盘单位变化
const handleDiskUnitChange = () => {
  nextTick(() => {
    if (ruleFormRef.value) {
      ruleFormRef.value.validateField('disk');
    }
  });
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;

				// 将带单位的数值转换回字节发送
				const memoryBytes = unitValueToBytes(parseFloat(formItem.mem) || 0, formItem.MEMunit);
				const storageBytes = unitValueToBytes(parseFloat(formItem.disk) || 0, formItem.DISKunit);

				quotaModify({
					user_id: formItem.userID,
					cpu_limit: formItem.cpu,
					memory_limit: memoryBytes,
					storage_limit: storageBytes,
				}).then((res:any) => {
          if(res.msg == 'ok') {
						ElMessage.success('配额分配操作完成');
					}else {
						ElMessage.error(res.msg);
					}
        })
			}
		});
	}
};
const dataQuery = () => {
  quotaQuery(formItem.userID)
  .then((res:any) => {
    formItem.cpu = res.data.cpu_limit;

    // 将内存字节数据转换为带单位的数据
    const memData = bytesToUnitValue(res.data.memory_limit);
    formItem.mem = memData.value.toString();
    formItem.MEMunit = memData.unit;

    // 将磁盘字节数据转换为带单位的数据
    const diskData = bytesToUnitValue(res.data.storage_limit);
    formItem.disk = diskData.value.toString();
    formItem.DISKunit = diskData.unit;
  })
	quotaMaxQuery().then((res:any) => {
    formItem.cpuMax = res.data.cpu_total;
    formItem.memMax = res.data.memory_total;
    formItem.diskMax = res.data.storage_total;
  })
};
const openDialog = (row:any) => {
  formItem.isShow = true;
	formItem.userName = row.username;
  formItem.userID = row.id;
  nextTick(() => {
    dataQuery()
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>