<template>
	<div class="chart-content">
    <el-button type="primary" plain @click="getOption">刷新</el-button>
    <div class="chart-box">
		  <my-echarts :options="state.chartOption"></my-echarts>
    </div>
	</div>
</template>

<script setup lang="ts" name="Echarts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));

const getOption = () => {
  let list = [25,50,80]
	let angle = 0; //角度，用来做简单的动画效果的
	let datas = list[Math.round(Math.random() * 2)]
	let jibie = '#f45d3f';
	let jibiecolor = '';
	if (datas <= 30) {
		jibie = '优';
		jibiecolor = '#2ebe76';
	} else if (datas > 30 && datas <= 70) {
		jibie = '良';
		jibiecolor = '#fcc65c';
	} else if (datas > 70) {
		jibie = '差';
		jibiecolor = '#f45d3f';
	}
	let value = datas;
	return {
		backgroundColor: '#000E1A00',
		title: {
			// text: "{a|" + value + "}{c|%}",
			text: '{a|' + jibie + '}\n{c|系统状态}',
			x: 'center',
			y: 'center',
			textStyle: {
				rich: {
					a: {
						fontSize: 48,
						lineHeight: 60,
						color: jibiecolor,
					},

					c: {
						fontSize: 20,
						color: '#ffffff',
						// padding: [5,0]
					},
				},
			},
		},
		series: [
			// 紫线
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					return {
						type: 'arc',
						shape: {
							cx: api.getWidth() / 2,
							cy: api.getHeight() / 2,
							r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
							startAngle: ((0 + angle) * Math.PI) / 180,
							endAngle: ((90 + angle) * Math.PI) / 180,
						},
						style: {
							stroke: '#8383FA',
							fill: 'transparent',
							lineWidth: 1.5,
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 紫点
			{
				name: 'ring5', //紫点
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					let x0 = api.getWidth() / 2;
					let y0 = api.getHeight() / 2;
					let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6;
					let point = getCirlPoint(x0, y0, r, 90 + angle);
					return {
						type: 'circle',
						shape: {
							cx: point.x,
							cy: point.y,
							r: 4,
						},
						style: {
							stroke: '#8450F9', //绿
							fill: '#8450F9',
						},
						silent: true,
					};
				},
				data: [0],
			},
			// 蓝线
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					return {
						type: 'arc',
						shape: {
							cx: api.getWidth() / 2,
							cy: api.getHeight() / 2,
							r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
							startAngle: ((180 + angle) * Math.PI) / 180,
							endAngle: ((270 + angle) * Math.PI) / 180,
						},
						style: {
							stroke: '#4386FA',
							fill: 'transparent',
							lineWidth: 1.5,
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 蓝点
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					let x0 = api.getWidth() / 2;
					let y0 = api.getHeight() / 2;
					let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6;
					let point = getCirlPoint(x0, y0, r, 270 + angle);
					return {
						type: 'circle',
						shape: {
							cx: point.x,
							cy: point.y,
							r: 4,
						},
						style: {
							stroke: '#4386FA', //绿
							fill: '#4386FA',
						},
						silent: true,
					};
				},
				data: [0],
			},
			// 橘线
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					return {
						type: 'arc',
						shape: {
							cx: api.getWidth() / 2,
							cy: api.getHeight() / 2,
							r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
							startAngle: ((90 + -angle) * Math.PI) / 180,
							endAngle: ((220 + -angle) * Math.PI) / 180,
						},
						style: {
							stroke: '#FF8E89',
							fill: 'transparent',
							lineWidth: 1.5,
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 橘点
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					let x0 = api.getWidth() / 2;
					let y0 = api.getHeight() / 2;
					let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65;
					let point = getCirlPoint(x0, y0, r, 90 + -angle);
					return {
						type: 'circle',
						shape: {
							cx: point.x,
							cy: point.y,
							r: 4,
						},
						style: {
							stroke: '#FF8E89', //粉
							fill: '#FF8E89',
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 绿线
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					return {
						type: 'arc',
						shape: {
							cx: api.getWidth() / 2,
							cy: api.getHeight() / 2,
							r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
							startAngle: ((270 + -angle) * Math.PI) / 180,
							endAngle: ((40 + -angle) * Math.PI) / 180,
						},
						style: {
							stroke: '#0CD3DB',
							fill: 'transparent',
							lineWidth: 1.5,
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 绿点
			{
				name: 'ring5',
				type: 'custom',
				coordinateSystem: 'none',
				renderItem: function (params:any, api:any) {
					let x0 = api.getWidth() / 2;
					let y0 = api.getHeight() / 2;
					let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65;
					let point = getCirlPoint(x0, y0, r, 270 + -angle);
					return {
						type: 'circle',
						shape: {
							cx: point.x,
							cy: point.y,
							r: 4,
						},
						style: {
							stroke: '#0CD3DB', //绿
							fill: '#0CD3DB',
						},
						silent: true,
					};
				},
				data: [0],
			},
      // 外环
			{
				name: '外环',
				type: 'pie',
				radius: ['52%', '40%'],
				silent: true,
				clockwise: true,
				startAngle: 90,
				z: 0,
				zlevel: 0,
				label: {
					normal: {
						position: 'center',
					},
				},
				data: [
					{
						value: value,
						name: '',
						itemStyle: {
							normal: {
								color: {
									// 完成的圆环的颜色
									colorStops: [
										{
											offset: 0,
											color: '#A098FC', // 0% 处的颜色
										},
										{
											offset: 0.3,
											color: '#4386FA', // 0% 处的颜色
										},
										{
											offset: 0.6,
											color: '#4FADFD', // 0% 处的颜色
										},
										{
											offset: 0.8,
											color: '#0CD3DB', // 100% 处的颜色
										},
										{
											offset: 1,
											color: '#646CF9', // 100% 处的颜色
										},
									],
								},
							},
						},
					},
					{
						value: 100 - value,
						name: '',
						label: {
							normal: {
								show: false,
							},
						},
						itemStyle: {
							normal: {
								color: '#173164',
							},
						},
					},
				],
			},
      // 内环
			{
				name: '内环',
				type: 'pie',
				radius: ['32%', '35%'],
				silent: true,
				clockwise: true,
				startAngle: 270,
				z: 0,
				zlevel: 0,
				label: {
					normal: {
						position: 'center',
					},
				},
				data: [
					{
						value: value,
						name: '',
						itemStyle: {
							normal: {
								color: {
									// 完成的圆环的颜色
									colorStops: [
										{
											offset: 0,
											color: '#00EDF3', // 0% 处的颜色
										},
										{
											offset: 1,
											color: 'red', // 100% 处的颜色
										},
									],
								},
							},
						},
					},
					{
						value: 100 - value,
						name: '',
						label: {
							normal: {
								show: false,
							},
						},
						itemStyle: {
							normal: {
								color: '#173164',
							},
						},
					},
				],
			},
		],
	};
};
const getCirlPoint=(x0:number, y0:number, r:number, angle:number)=> {
  let x1 = x0 + r * Math.cos((angle * Math.PI) / 180);
  let y1 = y0 + r * Math.sin((angle * Math.PI) / 180);
  return {
    x: x1,
    y: y1,
  };
};
const state = reactive({
	chartOption: getOption(),
});
</script>
<style scoped lang="scss">
.chart-content {
  width: 100%;
  height: 100%;
  .chart-box {
    width: 500px;
    height: 500px;
    border: 1px solid #ccc;
    background: #ccc;
  }
}
</style>