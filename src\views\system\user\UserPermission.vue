<template>
	<el-dialog v-model="formItem.isShow" :title="formItem.titles" append-to-body class="dialog-500">
		<div class="permission-area">
      <el-tree
		  	ref="treeRef"
		  	:data="treeData"
		  	node-key="id"
        show-checkbox
		  	:props="defaultProps"
		  	:expand-on-click-node='false'
		  	default-expand-all
		  	:filter-node-method="filterNode"
		  	@check="handleCheckChange"
        :default-checked-keys="formItem.checkedKey"
		  >
		  	<template #default="{ node, data }">
		  		<div class="custom-tree-node">
		  			<span class="node-label" :title="node.label">{{ node.label }}</span>
		  		</div>
		  	</template>
		  </el-tree>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="treeQuery">刷新</el-button>
				<el-button type="primary" @click="resetClick">重置权限</el-button>
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="UserPermission">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessageBox,ElMessage,FilterNodeMethodFunction, TreeInstance  } from 'element-plus';
import { powerTreeQuery,powerTreeEdit,powerTreeReset } from '/@/api/System'; // 接口
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具
const formItem = reactive({
	titles: '用户权限',
	isShow: false,
	userName: '',
	userRole: '',
	userID: '',
  checkedKey: [],
});
const treeRef = ref<TreeInstance>()
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'module',
}
const state = reactive({
	treeItem: {},
	searchValue: '',
});
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
// 搜索
const onSearch = () => {
	treeRef.value!.filter(state.searchValue)
};
// 处理节点勾选变化
const handleCheckChange = (data: any, checked: boolean, indeterminate: boolean) => {
	const checkedNodes = treeRef.value!.getCheckedNodes();
	const halfCheckedNodes = treeRef.value!.getHalfCheckedNodes();
	const checkedKeys = treeRef.value!.getCheckedKeys();
	const halfCheckedKeys = treeRef.value!.getHalfCheckedKeys();

	console.log('当前操作节点:', data);
	console.log('选中状态:', checked);
	console.log('半选状态:', indeterminate);
	console.log('所有选中的节点:', checkedNodes);
	console.log('所有半选的节点:', halfCheckedNodes);
	console.log('所有选中的节点ID:', checkedKeys);
	console.log('所有半选的节点ID:', halfCheckedKeys);

	// 更新 formItem.checkedKey 以保持同步
	formItem.checkedKey = checkedKeys as string[];
};
// 根据数据中的checked属性计算默认选中的节点
const calculateDefaultCheckedKeys = (nodes: any[]): string[] => {
	const checkedKeys: string[] = [];

	const traverse = (nodeList: any[]) => {
		nodeList.forEach(node => {
			if (node.checked === true) {
				checkedKeys.push(node.id);
			}
			if (node.children && node.children.length > 0) {
				traverse(node.children);
			}
		});
	};

	traverse(nodes);
	return checkedKeys;
};

// 检查父节点是否应该为半选状态
const shouldParentBeIndeterminate = (parentNode: any): boolean => {
	if (!parentNode.children || parentNode.children.length === 0) {
		return false;
	}

	const checkedChildren = parentNode.children.filter((child: any) => child.checked === true);
	const uncheckedChildren = parentNode.children.filter((child: any) => child.checked === false);

	// 如果有部分子节点选中，父节点应该为半选状态
	return checkedChildren.length > 0 && uncheckedChildren.length > 0;
};

// 更新父节点的选中状态
const updateParentCheckedStatus = (flatNodes: any[]) => {
	// 创建节点映射
	const nodeMap = new Map();
	flatNodes.forEach(node => {
		nodeMap.set(node.id, node);
	});

	// 从叶子节点向上更新父节点状态
	flatNodes.forEach(node => {
		if (node.pid && node.pid !== '0') {
			const parent = nodeMap.get(node.pid);
			if (parent) {
				const siblings = flatNodes.filter(n => n.pid === node.pid);
				const checkedSiblings = siblings.filter(n => n.checked === true);

				if (checkedSiblings.length === siblings.length) {
					// 所有子节点都选中，父节点也选中
					parent.checked = true;
				} else if (checkedSiblings.length > 0) {
					// 部分子节点选中，父节点为半选状态（在树组件中会自动处理）
					parent.checked = false;
				} else {
					// 所有子节点都未选中，父节点也未选中
					parent.checked = false;
				}
			}
		}
	});
};

const treeQuery = () => {
	const useTestData = true; // 设置为true使用测试数据，false使用API数据
	if (useTestData) {
		let zNodes = [
			{ id: '1', module: '资源节点', pid: '0', checked: true },
			{ id: '2', module: '主机池1', pid: '1', checked: true },
			{ id: '3', module: '集群1', pid: '2', checked: true },
			{ id: '4', module: '主机1', pid: '3', checked: true },
			{ id: '5', module: '主机2', pid: '3', checked: true },
			{ id: '6', module: '主机3', pid: '3', checked: true },
			{ id: '7', module: '主机4', pid: '3', checked: false },
			{ id: '8', module: '主机5', pid: '3', checked: false },
			{ id: '9', module: '主机6', pid: '3', checked: false },
		];

		// 更新父节点的选中状态
		updateParentCheckedStatus(zNodes);

		treeData.value = convertFlatToTree(zNodes, {
        idKey: 'id',
        pidKey: 'pid',
        childrenKey: 'children',
        rootValue: '0'
      });

      // 计算默认选中的节点
      formItem.checkedKey = calculateDefaultCheckedKeys(treeData.value);

      console.log('源数据',zNodes);
      console.log('重构之后数据',treeData.value);
      console.log('默认选中的节点:', formItem.checkedKey);
	} else {
		powerTreeQuery({
      username: formItem.userName,
      role: formItem.userRole
    }).then((res) => {
    	// 更新父节点的选中状态
    	updateParentCheckedStatus(res.data);

      treeData.value = convertFlatToTree(res.data, {
        idKey: 'id',
        pidKey: 'pid',
        childrenKey: 'children',
        rootValue: '0'
      });

      // 计算默认选中的节点
      formItem.checkedKey = calculateDefaultCheckedKeys(treeData.value);

      console.log('源数据',res.data);
      console.log('重构之后数据',treeData.value);
      console.log('默认选中的节点:', formItem.checkedKey);
		}).catch((error) => {
			console.error('获取权限树数据失败:', error);
			ElMessage.error('获取权限数据失败');
		});
	}
};
// 重置权限
const resetClick = () => {
  ElMessageBox.confirm(
    `是否对 <span style="font-weight: 800">${formItem.userName}</span> 登录账号进行重置权限操作？`,
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    powerTreeReset({user_id: formItem.userID})
    .then(res=>{
      ElMessage.success(formItem.userName+'登录账号权限重置完成')
    })
  })
  .catch(() => {})
};

// 确认
const confirm = () => {
  // powerTreeEdit({
  //   permissions: '全部节点',
  //   user_id: formItem.userID,
  //   user_role: formItem.userRole,
  // }).then(res=>{
  //   ElMessage.success(formItem.userName+'账号权限设置完成')
  //   formItem.isShow = false
  // }).catch((error) => {
  //   console.error('权限设置失败:', error);
  //   ElMessage.error('权限设置失败');
  // });
};
// 打开弹窗
const openDialog = async (row: any) => {
	formItem.titles = row.username + '用户权限';
	formItem.userName = row.username
	formItem.userRole = row.role_name
	formItem.userID = row.id
	formItem.isShow = true;
	nextTick(() => {
    setTimeout(() => {
      treeQuery()
    }, 500);
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.permission-area {
	height: 400px;
	// overflow: auto;
}
</style>