<template>
	<div class="monitoring-area layout-padding">
    <el-card>
      <div class="log-general-area">
        <div class="tabs-btn-area">
					<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
          <el-button type="primary" plain @click="enableClick(state.tableSelect)" v-if="powerItem.tianjia">启用</el-button>
          <el-button type="primary" plain @click="disableClick(state.tableSelect)" v-if="powerItem.shanchu">禁用</el-button>
          <!-- <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button> -->
        </div>
        <div class="tabs-table-area" v-if="powerItem.liebiao">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 类别 -->
						<template #job="{ row }">
							<span>{{ row.job==""?"集群告警":"存储告警"  }}</span>
						</template>
						<!-- 严重告警 -->
						<template #critical_value="{ row }">
							<template v-if="row.expr_code=='0'">
								<el-icon v-if="row.critical_value=='-1'" color="green"><Select /></el-icon>
								<span v-else>-</span>
							</template>
							<template v-if="row.expr_code!=='0'">
								<span v-if="row.critical_value=='0'">-</span>
								<span v-else>{{ '>=' }}{{ row.critical_value }}{{ row.unit }}</span>
							</template>
						</template>
						<!-- 重要告警 -->
						<template #major_value="{ row }">
							<template v-if="row.expr_code=='0'">
								<el-icon v-if="row.major_value=='-1'" color="green"><Select /></el-icon>
								<span v-else>-</span>
							</template>
							<template v-if="row.expr_code!=='0'">
								<span v-if="row.major_value=='0'">-</span>
								<span v-else>{{ '>=' }}{{ row.major_value }}{{ row.unit }}</span>
							</template>
						</template>
						<!-- 次要告警 -->
						<template #warning_value="{ row }">
							<template v-if="row.expr_code=='0'">
								<el-icon v-if="row.warning_value=='-1'" color="green"><Select /></el-icon>
								<span v-else>-</span>
							</template>
							<template v-if="row.expr_code!=='0'">
								<span v-if="row.warning_value=='0'">-</span>
								<span v-else>{{ '>=' }}{{ row.warning_value }}{{ row.unit }}</span>
							</template>
						</template>
						<!-- 提示告警 -->
						<template #info_value="{ row }">
							<template v-if="row.expr_code=='0'">
								<el-icon v-if="row.info_value=='-1'" color="green"><Select /></el-icon>
								<span v-else>-</span>
							</template>
							<template v-if="row.expr_code!=='0'">
								<span v-if="row.info_value=='0'">-</span>
								<span v-else>{{ '>=' }}{{ row.info_value }}{{ row.unit }}</span>
							</template>
						</template>
						<!-- 持续时间 -->
						<template #for_interval="{ row }">
							<span>{{row.for_interval.split('m')[0]}}分钟</span>
						</template>
						<!-- 状态 -->
						<template #status="{ row }">
							<el-tag :type="row.status == 'enabled'?'success':'info'">{{row.status == 'enabled'?'启用':'禁用'}}</el-tag>
						</template>
        		<!-- 操作 -->
						<template #operation="{ row }">
							<el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.xiugai || powerItem.tianjia || powerItem.shanchu">
								<el-button type="primary">操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
										<el-dropdown-item command="qy" v-if="powerItem.tianjia">启用</el-dropdown-item>
										<el-dropdown-item command="jy" v-if="powerItem.shanchu">禁用</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
							<span v-else>-</span>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
		<RulesEdit :tableRow='state.tableRow' :editTime='state.editTime' @returnOK="returnOK"></RulesEdit>
		<RulesAction :selectRow='state.selectRow' :actionType='state.actionType' :actionTime='state.actionTime' @returnOK="returnOK"></RulesAction>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Select,ArrowDownBold } from '@element-plus/icons-vue'
import { ruleColumns } from '/@/model/logManage'; // 表列、正则
import { alarmRulesQuery,alarmRulesEdit,alarmRulesAction } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const RulesAction = defineAsyncComponent(() => import('../generalPublic/RulesAction.vue'));
const RulesEdit = defineAsyncComponent(() => import('../generalPublic/RulesEdit.vue'));


// 定义变量内容
const state = reactive({
  columns: ruleColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
	selectRow: [],
	tableRow: {},
	editTime: '',
	actionTime: '',
	actionType: '',
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = []
	if(true){
		return new Promise(async(resolve)=>{
			alarmRulesQuery({
				page: page.pageNum, // 当前页
				pagecount: page.pageSize, // 每页显示条数
				order_type: page.order, // 排序规则
				order_by: page.sort, // 排序列
				search_str: '' // 搜索条件
			}).then((res:any)=>{
				resolve({
					data: res.data, // 数据
					total: res.total*1 // 总数
				})
			}).catch((err:any) => {
				resolve({
					data: [], // 数据
					total: 0 // 总数
				})
			})
		})
	}else {
		let list = [
			{
			"id": 1,
			"name": "服务器离线",
			"expr": "(up{job=\"node-exporter\"}) == 0",
			"expr_code": "0",
			"value": 0,
			"unit": null,
			"for_interval": "0m",
			"report_interval": "10m",
			"critical_value": -1,
			"major_value": 0,
			"warning_value": 0,
			"info_value": 0,
			"description": "服务器离线: VALUE = {{ $value }};  LABELS = {{ $labels }}",
			"summary": "服务器离线 (instance {{ $labels.instance }})",
			"job": null,
			"alert_type": null,
			"created_at": null,
			"updated_at": null,
			"is_default": 1,
			"status": "enabled",
			"order_id": 0
			},
			{
			"id": 2,
			"name": "节点失联",
			"expr": "up{job=\"openstacklinux\"} == 0 or up{job=\"openstackwindows\"} == 0",
			"expr_code": "0",
			"value": 0,
			"unit": null,
			"for_interval": "3m",
			"report_interval": "10m",
			"critical_value": -1,
			"major_value": 0,
			"warning_value": 0,
			"info_value": 0,
			"description": "节点失联: VALUE = {{ $value }};  LABELS = {{ $labels }}",
			"summary": "节点失联 (instance {{ $labels.instance }})",
			"job": null,
			"alert_type": null,
			"created_at": null,
			"updated_at": null,
			"is_default": 1,
			"status": "enabled",
			"order_id": 0
			},
			{
			"id": 4,
			"name": "节点CPU负载过高",
			"expr": "100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[2m])) * 100) > 90",
			"expr_code": "1",
			"value": 0,
			"unit": null,
			"for_interval": "0m",
			"report_interval": "10m",
			"critical_value": 90,
			"major_value": 80,
			"warning_value": 70,
			"info_value": 50,
			"description": "节点CPU负载过高: VALUE = {{ $value }};  LABELS = {{ $labels }}",
			"summary": "节点CPU负载过高 (instance {{ $labels.instance }})",
			"job": null,
			"alert_type": null,
			"created_at": null,
			"updated_at": null,
			"is_default": 1,
			"status": "enabled",
			"order_id": 0
			},
			{
			"id": 5,
			"name": "节点内存占用过高",
			"expr": "node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10",
			"expr_code": "1",
			"value": 0,
			"unit": null,
			"for_interval": "2m",
			"report_interval": "10m",
			"critical_value": 10,
			"major_value": 0,
			"warning_value": 0,
			"info_value": 0,
			"description": "节点内存占用过高: VALUE = {{ $value }};  LABELS = {{ $labels }}",
			"summary": "节点内存占用过高 (instance {{ $labels.instance }})",
			"job": null,
			"alert_type": null,
			"created_at": null,
			"updated_at": null,
			"is_default": 1,
			"status": "enabled",
			"order_id": 0
			},
			
		]
		return {
			data: list, // 数据
			total: list.length, // 总数
		};
		
	}
};
// 表操作列
const commandItem = (item: string,row:any)=>{
	state.selectRow = [row]
  switch (item) {
    case 'bj':
			state.tableRow = row
      state.editTime = ''+new Date()
      break;
		case 'qy':
      state.actionTime = ''+new Date()
      state.actionType = '启用'
      break;
		case 'jy':
      state.actionTime = ''+new Date()
      state.actionType = '禁用'
      break;
		case 'sc':
      state.actionTime = ''+new Date()
      state.actionType = '删除'
      break;
  }
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 启用
const enableClick = (arr:any)=>{
	if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.selectRow = arr
    state.actionTime = ''+new Date()
    state.actionType = '启用'
  }
}
// 禁用
const disableClick = (arr:any)=>{
	if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.selectRow = arr
    state.actionTime = ''+new Date()
    state.actionType = '禁用'
  }
}
// 删除
const deleteClick = (arr:any)=>{
	if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.selectRow = arr
    state.actionTime = ''+new Date()
    state.actionType = '删除'
  }
}
// 返回数据
const returnOK = (item:any)=>{
  refresh()
}
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'gaojingguizeliebiao',
    'gaojingguizeqiyong',
    'xiugaigaojingguize',
    'gaojingguizejinyong',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.gaojingguizeliebiao;
		powerItem.tianjia = res.data.gaojingguizeqiyong;
		powerItem.xiugai = res.data.xiugaigaojingguize;
		powerItem.shanchu = res.data.gaojingguizejinyong;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .log-general-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;

    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>