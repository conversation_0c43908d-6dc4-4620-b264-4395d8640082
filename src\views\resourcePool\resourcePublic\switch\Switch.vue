<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
				<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
        <el-button type="primary" plain @click="newClick" v-if="powerItem.tianjia">添加交换机</el-button>
        <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
      </div>
      <div v-if="powerItem.sousuo">
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area" v-if="powerItem.liebiao">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
        <!-- 集群 -->
        <!-- <template #colony="{ row }">
          <span>{{ row.cluster_name?row.cluster_name:'-' }}</span>
				</template> -->
        <!-- 物理机 -->
        <!-- <template #host="{ row }">
          <span>{{ row.host_hostname?row.host_hostname:'-' }}</span>
				</template> -->
        <!-- 类型 -->
        <template #type="{ row }">
          <span>{{ row.vswitch_type === 'local'?'本地交换机':'分布式交换机' }}</span>
				</template>
        <!-- 上行 -->
				<template #ascending="{ row }">
					<el-button type="primary" link @click="ascendClick(row)" v-if="powerItem.shangxing"
						><el-icon><View /></el-icon> 上行</el-button
					>
					<span v-else>-</span>
				</template>
				<!-- 下行 -->
				<template #descending="{ row }">
					<el-button type="primary" link @click="descendClick(row)" v-if="powerItem.xiaxing"
						><el-icon><View /></el-icon> 下行</el-button
					>
				</template>
        <!-- 端口组 -->
				<template #port="{ row }">
          <el-button type="primary" link @click="portClick(row)" v-if="powerItem.duankouzu"
						><el-icon><View /></el-icon> 端口组</el-button
					>
					<span v-else>-</span>
				</template>
        <!-- 操作 -->
				<template #operation="{ row }">
					<el-dropdown trigger="click" @command="commandItem($event, row)" v-if="powerItem.xiugai || powerItem.shanchu">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item command="bj" disabled>策略</el-dropdown-item> -->
								<el-dropdown-item command="bj" disabled v-if="powerItem.xiugai">修改</el-dropdown-item>
								<el-dropdown-item command="sc" style="color: red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
					<span v-else>-</span>
				</template>
			</my-table>
    </div>
    <PortGroup ref="groupRef" />
    <Ascending ref="ascendRef" />
    <Descending ref="descendRef" />
    <TableNew ref="newRef" @returnOK="returnOK" />
    <TableEdit ref="editRef" @returnOK="returnOK" />
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search, View,ArrowDownBold } from '@element-plus/icons-vue'
import { localSwitchColumns } from '/@/model/network'; // 表列、正则
import { localSwitchQuery,localSwitchDelete } from '/@/api/Network'; // 接口
import { dayjs,ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const PortGroup = defineAsyncComponent(() => import('./portGroup/index.vue'))
const Ascending = defineAsyncComponent(() => import('./ascending/index.vue'))
const Descending = defineAsyncComponent(() => import('./descending/index.vue'))
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  tableSearch: '',
  columns: localSwitchColumns as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async(resolve)=>{
    await localSwitchQuery({
      host_id: props.treeItem.id,
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页显示条数
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      // page: page.pageNum?page.pageNum:1, // 当前页
      // pagecount: page.pageSize?page.pageSize:10, // 每页显示条数
      // order_type: page.order?page.order:'asc', // 排序规则
      // order_by: page.sort?page.sort:'', // 排序列
      search_str: state.tableSearch // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {})
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
const groupRef = ref(); //  端口组
const ascendRef = ref(); //  上行
const descendRef = ref(); //  下行
const newRef = ref(); //  添加
const editRef = ref(); //  修改
// 端口组
const portClick = (row:any)=>{
  groupRef.value.openDialog(row);
}
// 上行
const ascendClick = (row: any) => {
  ascendRef.value.openDialog(row);
};
// 下行
const descendClick = (row: any) => {
  descendRef.value.openDialog(row);
};
// 添加交换机
const newClick = () => {
  newRef.value.openDialog(props.treeItem);
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  switch (item) {
    case 'bj':
      editRef.value.openDialog(row,props.treeItem);
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 删除存储池
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '虚拟交换机/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    localSwitchDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.code == 200){
        setTimeout(() => {
          refresh()
        }, 1000);
        ElMessage.info(res.msg);
      }else {
        ElMessage.error('删除虚拟交换机操作失败');
      }
    })
  }else {
    refresh()
  }
}
watch(
  ()=> props.treeItem,
  (val)=>{
    if(tableRef.value){
			refresh();
		}
  }
);
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
  shangxing: false,
  xiaxing: false,
  duankouzu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'bendijiaohuanjiliebiao',
    'bendijiaohuanjisousuo',
    'tianjiabendijiaohuanji',
    'xiugaibendijiaohuanji',
    'shanchubendijiaohuanji',
    'shangxing',
    'xiaxing',
    'duankouzu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.bendijiaohuanjiliebiao;
		powerItem.sousuo = res.data.bendijiaohuanjisousuo;
		powerItem.tianjia = res.data.tianjiabendijiaohuanji;
		powerItem.xiugai = res.data.xiugaibendijiaohuanji;
		powerItem.shanchu = res.data.shanchubendijiaohuanji;
		powerItem.shangxing = res.data.shangxing;
		powerItem.xiaxing = res.data.xiaxing;
		powerItem.duankouzu = res.data.duankouzu;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>