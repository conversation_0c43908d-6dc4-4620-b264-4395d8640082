/**
 * 清除对象属性
 * @param obj
 */
export function deleteKeysforObject(obj: EmptyObjectType) {
	Object.keys(obj).map((key) => {
		delete obj[key];
	});
}

/**
 * 格式化统一输出文件大小
 * @param size
 */
export function formatSize(size: number) {
	const list = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB'];
	for (let i = 0; i < list.length; i++) {
		if (size < Math.pow(1024, i + 1)) {
			return parseFloat((size / Math.pow(1024, i)).toFixed(2)) + list[i];
		}
	}
	return '--';
}

/**
 * 根据时间戳计算出距当前x天x时等
 * @param time 时间戳
 */
export function formatTime(time: number = 127614240000) {
	const list = [
		['年', 1000 * 60 * 60 * 24 * 365],
		['月', 1000 * 60 * 60 * 24 * 30],
		['天', 1000 * 60 * 60 * 24],
		['时', 1000 * 60 * 60],
		['分', 1000 * 60],
		['秒', 1000],
	] as EmptyArrayType;
	let result = '';
	for (let i = 0; i < list.length; i++) {
		if (time / list[i][1] >= 1) {
			result += `${parseInt(time / list[i][1] + '')}${list[i][0]}`;
			if ((time % list[i][1]) > 0) {
				time = time % list[i][1];
			}else{
				break;
			}
		}
	}

	return result;
}
