const fs = require('fs');
const path = require('path');

// 递归查找所有 .ts 文件
function findTsFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findTsFiles(filePath));
      }
    } else {
      const ext = path.extname(file);
      if (ext === '.ts') {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的表格列类型问题
function fixTableTypesInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复表格列定义中的 unknown 类型
    const patterns = [
      // 修复 prop 属性
      { from: /prop\?\s*:\s*unknown/g, to: 'prop?: string' },
      // 修复 label 属性
      { from: /label\?\s*:\s*unknown/g, to: 'label?: string' },
      // 修复 width 属性
      { from: /width\?\s*:\s*unknown/g, to: 'width?: string | number' },
      // 修复 align 属性
      { from: /align\?\s*:\s*unknown/g, to: 'align?: "left" | "center" | "right"' },
      // 修复 tdSlot 属性
      { from: /tdSlot\?\s*:\s*unknown/g, to: 'tdSlot?: string' },
      // 修复 sortable 属性
      { from: /sortable\?\s*:\s*unknown/g, to: 'sortable?: boolean' },
      // 修复 wrap 属性
      { from: /wrap\?\s*:\s*unknown/g, to: 'wrap?: boolean' },
      // 修复 type 属性
      { from: /type\?\s*:\s*unknown/g, to: 'type?: string' },
    ];
    
    patterns.forEach(pattern => {
      if (pattern.from.test(content)) {
        content = content.replace(pattern.from, pattern.to);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed table types in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding TypeScript files to fix table types...');
  
  const files = findTsFiles(srcDir);
  console.log(`Found ${files.length} TypeScript files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixTableTypesInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed table types in ${fixedCount} files`);
}

main();
