<template>
	<el-card>
		<div>
			<el-radio-group v-model="state.automatic" @change="migrateChange">
				<el-radio-button value="off">关闭</el-radio-button>
				<el-radio-button value="on">开启</el-radio-button>
			</el-radio-group>
		</div>
	</el-card>
</template>
<script setup lang="ts" name="Migration">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { automaticShutdownQuery, automaticShutdownEdit } from '/@/api/System'; // 接口
const state = reactive({
	automatic: 'off',
});
// 宕机迁移查询
const migrateQuery = () => {
	automaticShutdownQuery().then((res) => {
		if (res.enabled_auto) {
			state.automatic = res.enabled_auto;
		}
	});
};
// 宕机迁移修改
const migrateChange = (item: string) => {
	automaticShutdownEdit({ enabled_auto: item }).then((res) => {
		if (res.msg == 'ok') {
			ElMessage.success('自动宕机设置' + item == 'off' ? '关闭' : '开启' + '完成');
		} else {
			ElMessage.error('自动宕机设置' + item == 'off' ? '关闭' : '开启' + '完成');
		}
	});
};
onMounted(() => {
	// migrateQuery()
});
</script>