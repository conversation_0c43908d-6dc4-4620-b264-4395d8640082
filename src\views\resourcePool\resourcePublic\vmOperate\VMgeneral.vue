<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否对下列虚拟机进行 <span :style="{color:formItem.color}">{{ formItem.type }}</span> 操作？</span>
      <p :style="{color:formItem.color,'word-wrap': 'break-word'}">{{ formItem.names.toString() }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { vmOpen,vmClose,vmDestroy,vmPause,vmRestore,vmRestart,vmForceRestart,vmFullClone,vmLinkClone } from '/@/api/ResourcePool/vm'; // 接口
import { ElMessage } from 'element-plus';
const formItem = reactive({
  isShow: false,
  title: '删除',
  type: '删除',
  color: 'green',
  names: [''],
  ids: [''],
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.isShow = false;
  let data = {
    names: formItem.names,
    ids: formItem.ids
  };
  if(formItem.type=='开机'){
    vmOpen(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机开机操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='关机'){
    vmClose(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机关机操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='关闭电源'){
    vmDestroy(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机关闭电源操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='暂停'){
    vmPause(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机暂停操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='恢复'){
    vmRestore(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机恢复操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='重启'){
    vmRestart(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机重启操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='强制重启'){
    vmForceRestart(data).then((res:any)=>{
      if(res.msg == 'ok') {
        ElMessage.success('虚拟机强制重启操作完成');
        setTimeout(() => {
          emit('returnOK', 'refresh');
        }, 1000);
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else if(formItem.type=='完全克隆'){
    formItem.ids.forEach((item:any,index:any) => {
      vmFullClone({vm_id: item,vm_name: formItem.names[index]}).then((res:any)=>{
        if(res.msg == 'ok') {
          ElMessage.success(formItem.names[index]+'虚拟机完全克隆操作完成');
          setTimeout(() => {
            emit('returnOK', 'refresh');
          }, 1000);
        }else {
          ElMessage.error(res.msg);
        }
      })
    });
  }else if(formItem.type=='链接克隆'){
    formItem.ids.forEach((item:any,index:any) => {
      vmLinkClone({vm_id: item,vm_name: formItem.names[index]}).then((res:any)=>{
        if(res.msg == 'ok') {
          ElMessage.success(formItem.names[index]+'虚拟机链接克隆操作完成');
          setTimeout(() => {
            emit('returnOK', 'refresh');
          }, 1000);
        }else {
          ElMessage.error(res.msg);
        }
      })
    });
  }
}
// 打开弹窗
const openDialog = async (type:string,color:string,rows:any) => {
  formItem.isShow = true;
  nextTick(() => {
    formItem.title = type+'虚拟机'
    formItem.type = type
    formItem.color = color
    let names:any[] = [];
    let ids:any[] = [];
    rows.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formItem.names = names
    formItem.ids = ids
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>