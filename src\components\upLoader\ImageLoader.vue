<template>
	<el-upload
		v-if="!imageBase64"
		v-model:file-list="fileList"
		:action="action"
		:multiple="multiple"
		:limit="limit"
		:on-exceed="handleExceed"
		:before-upload="beforeUpload"
		:on-success="handleSuccess"
		:on-error="handleError"
		:on-remove="handleRemove"
		:accept="computedAccept"
		:show-file-list="showFileList"
		list-type="picture-card"
		:disabled="disabled"
		:headers="headers"
		:on-change="handleChangeUpload"
		:auto-upload="false"
	>
		<el-icon><Plus /></el-icon>

		<template #tip>
			<div class="el-upload__tip set-tip" v-if="tip">
				{{ tip }}
			</div>
			<div class="el-upload__tip" v-else>
				请上传{{ fileType && fileType.length ? ` ${formatAcceptTypes()} ` : '' }}文件{{ limit && limit > 1 ? `，最多 ${limit} 个` : ''
				}}{{ fileSize ? `，每个不超过 ${fileSize}MB` : '' }}
			</div>
		</template>

		<template #file="{ file }">
			<div class="upload-file-container">
				<img v-if="file.url && isImage(file)" class="upload-file-image" :src="file.url" />
				<div v-else class="upload-file-default">
					<el-icon :size="50"><Document /></el-icon>
				</div>
				<span class="upload-file-name">{{ file.name }}</span>
				<span class="upload-file-actions">
					<span class="upload-file-preview" @click="handlePreview(file)">
						<el-icon><ZoomIn /></el-icon>
					</span>
					<span v-if="!disabled" class="upload-file-delete" @click="handleRemove(file)">
						<el-icon><Delete /></el-icon>
					</span>
				</span>
			</div>
		</template>
	</el-upload>
	<div v-if="imageBase64">
		<el-image
			:src="imageBase64"
			fit="cover"
			v-show="false"
			:style="{ width: uploadWidth + 'px', height: uploadHeight + 'px' }"
			class="image64-wrap"
			:preview-src-list="imageBase64 ? [imageBase64] : []"
		>
		</el-image>
		<div class="image64-wrap set-image" :style="{ backgroundImage: `url(${imageBase64})`, width: uploadWidth + 'px', height: uploadHeight + 'px' }">
			<el-icon class="delete-icon"><Delete @click="handleRemoveImage" /></el-icon>
		</div>
		<div class="el-upload__tip set-tip" v-if="tip">
			{{ tip }}
		</div>
	</div>
	<el-dialog v-model="dialogVisible" title="预览" width="50%">
		<img w-full :src="dialogImageUrl" alt="预览图片" />
	</el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Document, Delete, ZoomIn } from '@element-plus/icons-vue';

const props = defineProps({
	// 上传的地址
	action: {
		type: String,
		required: true,
	},
	// 默认已上传的文件列表
	modelValue: {
		type: Array,
		default: () => [],
	},
	// 是否支持多选文件
	multiple: {
		type: Boolean,
		default: true,
	},
	// 最大允许上传个数
	limit: {
		type: Number,
		default: 0,
	},
	// 文件大小限制(MB)
	fileSize: {
		type: Number,
		default: 0,
	},
	// 接受上传的文件类型数组
	fileType: {
		type: Array,
		default: () => [],
	},
	// 提示文字
	tip: {
		type: String,
		default: '',
	},
	// 是否禁用
	disabled: {
		type: Boolean,
		default: false,
	},
	// 上传宽度
	uploadWidth: {
		type: Number,
		default: 100,
	},
	// 上传高度
	uploadHeight: {
		type: Number,
		default: 100,
	},
	keys: {
		// 要修改的key
		type: String,
		default: 'files',
	},
	imageBase64: {
		// 预览的图片
		type: String,
		default: '',
	},
	// 是否显示文件列表
	showFileList: {
		type: Boolean,
		default: false,
	},
});

const headers = ref({
	Token: window.sessionStorage.getItem('vrts-token'),
});
const emit = defineEmits(['update:modelValue', 'success', 'error', 'remove', 'updateImageBase64', 'removeKeys']);

const fileList = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value),
});

const dialogVisible = ref(false);
const dialogImageUrl = ref('');

// 将数组格式的 fileType 转换为字符串格式供 el-upload 使用
const computedAccept = computed(() => {
	return props.fileType.join(',');
});

// 格式化显示可接受的文件类型
const formatAcceptTypes = () => {
	return props.fileType
		.map((type) => {
			if (type.startsWith('.')) {
				return type.toUpperCase();
			}
			return type;
		})
		.join('、');
};

// 判断文件是否为图片
const isImage = (file) => {
	return file.raw?.type.includes('image') || file.url?.includes('data:image') || file.url?.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i);
};

// 文件超出数量限制时的钩子
const handleExceed = () => {
	ElMessage.warning(`最多只能上传 ${props.limit} 个文件`);
};

// 上传前的校验
const beforeUpload = (file) => {
	// 校验文件类型
	if (props.fileType && props.fileType.length > 0) {
		const fileExtension = file.name.split('.').pop().toLowerCase();
		const isValidType = props.fileType.some((type) => {
			// 处理类似 ".pdf" 这样的扩展名
			if (type.startsWith('.')) {
				return fileExtension === type.substring(1).toLowerCase();
			}
			// 处理类似 "image/*" 这样的MIME类型
			else if (type.includes('/*')) {
				const mainType = type.split('/*')[0];
				return file.type.startsWith(mainType);
			}
			// 处理完整的MIME类型
			else {
				return file.type === type;
			}
		});

		if (!isValidType) {
			ElMessage.warning(`只能上传 ${formatAcceptTypes()} 格式的文件`);
			return false;
		}
	}

	// 校验文件大小
	if (props.fileSize) {
		const isLt = file.size / 1024 / 1024 < props.fileSize;
		if (!isLt) {
			ElMessage.warning(`上传文件大小不能超过 ${props.fileSize}MB!`);
			return false;
		}
	}

	return true;
};
const handleRemoveImage = () => {
	ElMessageBox.confirm('确定要删除这张图片吗?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			emit('removeKeys', props.keys);
		})
		.catch(() => {});
};
const handleChangeUpload = (file) => {
	if (!file) return;
	const reader = new FileReader();
	reader.onload = (event) => {
		// this.fileBase64 = event.target.result;
		let imageBase64String = '';
		imageBase64String = event.target.result;
		emit('updateImageBase64', imageBase64String, props.keys);
		// 这里可以调用上传方法或其他处理
	};
	reader.readAsDataURL(file.raw);
	return;
};
// 文件上传成功时的钩子
const handleSuccess = (response, uploadFile, uploadFiles) => {
	fileList.value = uploadFiles;
	emit('success', response, uploadFile, uploadFiles);
};

// 文件上传失败时的钩子
const handleError = (error, uploadFile, uploadFiles) => {
	ElMessage.error('上传失败111');
	emit('error', error, uploadFile, uploadFiles);
};

// 文件移除时的钩子
const handleRemove = (uploadFile, uploadFiles) => {
	fileList.value = uploadFiles;
	emit('remove', uploadFile, uploadFiles);
};

// 点击已上传的文件链接时的钩子
const handlePreview = (file) => {
	dialogImageUrl.value = file.url || URL.createObjectURL(file.raw);
	dialogVisible.value = true;
};
</script>

<style scoped>
.upload-demo {
	width: 100%;
}

.upload-file-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.upload-file-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.upload-file-default {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	color: #8c939d;
}

.upload-file-name {
	display: block;
	margin-top: 8px;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.upload-file-actions {
	position: absolute;
	top: 0;
	right: 0;
	display: none;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
}

.upload-file-container:hover .upload-file-actions {
	display: flex;
}

.upload-file-preview,
.upload-file-delete {
	margin: 0 8px;
	cursor: pointer;
}

.el-icon {
	font-size: 20px;
}
.el-upload--picture-card {
	height: 100%;
}
.set-tip {
	color: #c8c9cc;
}
.image64-wrap {
	border: 1px #cdd0d6 solid;
	position: relative;
	&:hover {
		border-color: #fe6902;
		border: 1px #fe6902 dashed;
		.delete-icon {
			display: block;
			color: #fe6902;
		}
	}
}

.custom-image:hover .image-overlay {
	opacity: 1;
}

.delete-icon {
	color: white;
	font-size: 24px;
	display: none;
	cursor: pointer;
}
.set-image {
	background-size: cover;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
