<template>
	<div class="dialog-tree-body">
		<div class="search-area">
			<el-input v-model="formItem.searchValue" class="search-input" placeholder="搜索节点" :prefix-icon="Search" @input="onSearch" />
			<!-- <el-button >勾选主机状态</el-button> -->
		</div>
		<div class="tree-area">
			<ul ref="treeRef"  class="ztree"></ul>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
const props = defineProps({
	type: {
		type: String,
		required: true,
	},
	zNodes: {
		type: Array,
		required: true,
	},
});
const treeRef = ref<HTMLElement | null>(null); 
const formItem = reactive({
	selectNodes: [],
	searchValue: '',
	zTreeObj: null as any, // 树形控件对象
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
import ic3_0 from '/@/assets/resource/pro0.png';
import ic3_1 from '/@/assets/resource/pro1.png';
import ic3_2 from '/@/assets/resource/pro2.png';
import ic3_3 from '/@/assets/resource/pro3.png';
import ic3_4 from '/@/assets/resource/pro4.png';

// 定义图标
type NodeStatus = 'up' | 'down' | 'info' | '-';
type NodeType = 'ziyuan' | 'pool' | 'cluster' | 'host' | 'domain';
const customizeIcon = (treeId: string, treeNode:{tId: string,level:number,status:NodeStatus,type:NodeType} ) => {
  let $icon = $("#" + treeNode.tId + "_ico");
	const defaultIcons = [ic0, ic1, ic2, ic3, ic4];
	const statusIcons: Record<NodeStatus, string> = {
		up: ic3_0,
		down: ic3_1,
		info: ic3_2,
		'-': '', // 可以提供默认情况，若无图标则为空
	};
	const typeIcons: Record<string, string> = {
    ziyuan: ic0,
    pool: ic1,
    cluster: ic2,
    host: ic3,
    domain: ic4,
  };
  // const icon = statusIcons[treeNode.status] || defaultIcons[treeNode.level]; // 根据 level 获取对应图标
  // const icon = statusIcons[treeNode.status] ; // 根据 level 获取对应图标
	// const icon = (treeNode.status && treeNode.status !== '-') 
  //   ? statusIcons[treeNode.status] 
  //   : typeIcons[treeNode.type || ''];
  // if (icon) {
  //   $icon.css({
  //     background: `url(${icon}) no-repeat center center`,
  //     backgroundSize: '100% 100%',
  //   });
  // }
};
const emit = defineEmits(['returnOK']);
// 已勾选节点
const treeChecked = (event: any, treeId: string, treeNode: any) => {
	const treeObj = formItem.zTreeObj;
	if (!treeObj) return;
	let parentNode = treeNode.getParentNode(); // 获取父节点
	 // 获取所有兄弟节点（包括当前点击的节点）
	if (parentNode) {
    let siblings = parentNode.children || [];
    // 检查是否所有兄弟节点都被选中
    let allSiblingsChecked = siblings.every((node: any) => node.checked);
    if (allSiblingsChecked) {
      // 如果所有兄弟节点都选中了，则选中父节点
      treeObj.checkNode(parentNode, true, false);
    } else {
      // 只要有一个子节点未选中，就取消父节点的选中状态
      treeObj.checkNode(parentNode, false, false);
    }
  }
	// 记录已勾选的节点
  // formItem.selectNodes = treeObj.getCheckedNodes(true);
  // emit('returnOK', formItem.selectNodes);
	// 获取当前选中的所有节点
	// formItem.selectNodes = formItem.zTreeObj.getCheckedNodes(true).filter((node: any) => node.level === 4);
	formItem.selectNodes = formItem.zTreeObj.getCheckedNodes(true); // 获取选中的节点
	
	if(formItem.selectNodes.length==0) {
    emit('returnOK', treeNotChecked(),'none');
	}else {
    emit('returnOK', formItem.selectNodes);
	}

	// 在方法最后将选中的节点传递回去
	emit('returnOK', formItem.selectNodes);
};
// 未勾选节点
const treeNotChecked = () => {
  if (!formItem.zTreeObj) return [];
  let nodesFalse = formItem.zTreeObj.transformToArray(formItem.zTreeObj.getNodes()).filter((node:any) => !node.checked)
  let listFalse = nodesFalse.filter((node: any) => node.level === 4) // 先筛选出 level 为 4 的节点
  return listFalse
};
const setting = {
	check: {
		enable: true, // 启用 checkbox
		chkboxType: { Y: 's', N: 's' }, // 级联勾选（Y: 选中时子节点是否选中，N: 取消选中时子节点是否取消）
	},
	data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
	},
	view: {
		showIcon: true, // 显示图标
		addDiyDom: customizeIcon, // 自定义图标
		fontCss: (treeId: any, treeNode: any) => (treeNode.highlight ? { color: 'red', 'font-weight': 'bold' } : { color: '#333' }),
	},
	callback: {
		beforeClick: function (treeId:string, treeNode:any) {
        return false; // 直接返回 false，不让节点被高亮选中
    },
		onCheck: treeChecked,
	},
};

// 初始化
const init = () => {
	if (treeRef.value) {
		formItem.zTreeObj = 
		(window as any).$.fn.zTree.init(
        (window as any).$(treeRef.value), // 使用 ref
        setting,
        props.zNodes
      );// 初始化树形控件
		// window.$.fn.zTree.init($('#treeDemo'), setting, props.zNodes); // 初始化树形控件
		formItem.zTreeObj.expandAll(true); // 默认展开所有树的分支
    nextTick(() => {
			// 确保树已初始化后再设置选中状态
			if (props.zNodes) {
				let nodes = formItem.zTreeObj.getCheckedNodes(true);
				nodes.forEach((node: any) => {
					node.checked = false;
					formItem.zTreeObj.updateNode(node);
				});
				props.zNodes.forEach((node: any) => {
					if (node.checked) {
					}
				});
			}
    });
	}
};
// 搜索
const onSearch = () => {
	let zTreeObj = formItem.zTreeObj; // 初始化树形控件
	if (!zTreeObj) return;
	const value = formItem.searchValue.trim();
	const allNodes = zTreeObj.transformToArray(zTreeObj.getNodes());

	// 清除所有节点的高亮状态
	allNodes.forEach((node: any) => {
		node.highlight = false;
		zTreeObj.updateNode(node);
	});

	// 如果搜索值为空，直接返回
	if (!value) return;

	// 获取匹配的节点
	const matchedNodes = zTreeObj.getNodesByParamFuzzy('name', value);

	// 高亮匹配的节点并展开父节点
	matchedNodes.forEach((node: any) => {
		node.highlight = true;
		zTreeObj.updateNode(node);
		zTreeObj.expandNode(node.getParentNode(), true);
	});
};
const updateTreeData = (newNodes: any[]) => {
  let treeObj = formItem.zTreeObj;
  // 先获取当前所有节点
  let allNodes = treeObj.transformToArray(treeObj.getNodes());

  // 记录原来选中的节点 ID
  let checkedNodeIds = allNodes
    .filter((node: any) => node.checked)
    .map((node: any) => node.id);

  // 清空当前树的所有节点
  allNodes.forEach((node: any) => {
    treeObj.removeNode(node);
  });
  // 重新添加新节点
  treeObj.addNodes(null, newNodes);
  // 恢复之前选中的节点
  checkedNodeIds.forEach((id:string) => {
    let node = treeObj.getNodeByParam("id", id);
    if (node) {
      treeObj.checkNode(node, true, false);
    }
  });
	formItem.zTreeObj.expandAll(true); // 默认展开所有树的分支

	// 获取所有选中的节点并返回
	formItem.selectNodes = formItem.zTreeObj.getCheckedNodes(true);
	if(formItem.selectNodes.length==0) {
    emit('returnOK', treeNotChecked(),'none');
  }else {
    emit('returnOK', formItem.selectNodes);
  }
};
watch(
	() => props.type,
	(val) => {
		init()
	}
);
watch(
	() => props.zNodes,
	(val) => {
		if (formItem.zTreeObj) {
      updateTreeData(val);
    }
	}
);
// watch(
//   () => [props.type, props.zNodes],
//   () => {
// 		init()
// 		console.log('未勾选',treeNotChecked())
// 		console.log('已勾选',formItem.selectNodes)
//   },
//   { deep: true }
// );
</script>
<style lang="scss" scoped>
.dialog-tree-body {
	width: 100%;
	height: 100%;
	padding: 10px;
	border: 1px solid #ccc;
	.search-area {
		height: 35px;
		display: flex;
		.search-input {
			margin-right: 10px;
		}
	}
	.tree-area {
		// width: 50%;
		width: 100%;
		height: calc(100% - 50px);
		overflow: auto;
    margin: 0 auto;
	}
}
</style>