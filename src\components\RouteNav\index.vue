<template>
  <el-tabs 
    v-model="activeName" 
    class="th-tabs"
    @tab-click="handleTabClick"
  >
    <el-tab-pane 
      v-for="item in routeList" 
      :key="item.path"
      :label="setTagsViewNameI18n(item)" 
      :name="item.path"
    >
      <!-- 空内容或者可以完全去掉内容 -->
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import other from '/@/utils/other';

// 定义类型
type RouteToFrom = {
  path: string;
  name?: string | symbol | undefined | null;
  redirect?: string;
  meta?: {
    title?: string;
    isLink?: string;
    isHide?: boolean;
    isKeepAlive?: boolean;
    isAffix?: boolean;
    isIframe?: boolean;
    roles?: string[];
    icon?: string;
    isDynamic?: boolean;
    isDynamicPath?: string;
    isIframeOpen?: string;
    loading?: boolean;
  };
  children?: RouteToFrom[];
  query?: { [key: string]: any };
  params?: { [key: string]: any };
  url?: string;
  [key: string]: any;
};

// 定义props
const props = defineProps({
  // 路由列表
  routes: {
    type: Array as () => RouteToFrom[],
    default: () => [],
  },
  // 是否自动获取子路由
  autoGetChildren: {
    type: Boolean,
    default: true,
  },
  // 自定义样式类名
  customClass: {
    type: String,
    default: '',
  },
  // tabs 类型
  tabsType: {
    type: String,
    default: 'border-card',
  }
});

// 定义事件
const emit = defineEmits(['click']);

const route = useRoute();
const router = useRouter();
const routeList = ref<RouteToFrom[]>([]);
const routePath = ref('');
const activeName = ref('');

// 设置 自定义 tagsView 名称、 自定义 tagsView 名称国际化
const setTagsViewNameI18n = computed(() => {
  return (v: RouteToFrom) => {
    return other.setTagsViewNameI18n(v);
  };
});

// 点击路由项
const handleClick = (v: RouteToFrom) => {
  routePath.value = v.path || '';
  activeName.value = v.path || '';
  router.push(v.path || '/');
  emit('click', v);
};

// 处理 tab 点击
const handleTabClick = (pane: any) => {
  const targetRoute = routeList.value.find(item => item.path === pane.paneName);
  if (targetRoute) {
    handleClick(targetRoute);
  }
};

// 更新激活的 tab
const updateActiveTab = () => {
  const currentRoute = routeList.value.find(item => 
    item.path === route.path
  );
  if (currentRoute) {
    activeName.value = currentRoute.path;
    routePath.value = currentRoute.path;
  } else if (routeList.value.length > 0) {
    // 如果没有找到匹配的路由，默认激活第一个
    activeName.value = routeList.value[0].path;
  }
};

// 获取子路由列表
const getChildRoutes = () => {
  if (props.autoGetChildren) {
    // 获取父级路由，route.matched.length - 2 是父级路由
    const parentRouteRecord = route.matched[route.matched.length - 2];
    if (parentRouteRecord && parentRouteRecord.children) {
      routeList.value = parentRouteRecord.children as RouteToFrom[];
    }
  } else {
    routeList.value = props.routes;
  }
  
  // 设置当前激活的 tab
  updateActiveTab();
};

// 监听路由变化
watch(() => route.path, () => {
  getChildRoutes();
  routePath.value = route.path;
  updateActiveTab();
}, { immediate: true });

// 监听props.routes变化
watch(() => props.routes, () => {
  if (!props.autoGetChildren) {
    routeList.value = props.routes;
    updateActiveTab();
  }
}, { immediate: true });
</script>

<style scoped lang="scss">

</style> 