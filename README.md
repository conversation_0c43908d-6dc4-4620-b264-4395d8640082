# HCI 超融合基础设施管理平台

## 项目简介

HCI 超融合基础设施管理平台是一个基于 Vue 3 + TypeScript 的现代化超融合基础设施管理系统，提供了完整的计算、存储、网络资源的统一管理和监控功能。系统采用前后端分离架构，支持虚拟化环境的全生命周期管理。

## 🚀 技术栈

- **前端框架**: Vue 3.x + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **UI组件库**: Element Plus
- **样式预处理**: SCSS
- **图表组件**: ECharts + ECharts GL + ECharts WordCloud
- **国际化**: Vue I18n
- **HTTP客户端**: Axios
- **代码编辑器**: Monaco Editor
- **富文本编辑**: WangEditor
- **文件上传**: Uppy
- **表格导出**: js-table2excel
- **开发工具**: ESLint + Prettier

## 📁 项目结构

```
hci_vue3/
├── public/                    # 静态资源
│   ├── favicon.png           # 网站图标
│   ├── version.json          # 版本信息
│   └── novnc/                # VNC远程连接资源
├── src/
│   ├── api/                  # API接口
│   │   ├── Overview/         # 概览相关接口
│   │   ├── ResourcePool/     # 资源池相关接口
│   │   ├── StoreManage/      # 存储管理接口
│   │   ├── Network/          # 网络管理接口
│   │   ├── Monitoring/       # 监控告警接口
│   │   ├── System/           # 系统管理接口
│   │   └── login/            # 登录接口
│   ├── assets/               # 静态资源
│   ├── components/           # 公共组件
│   ├── directive/            # 自定义指令
│   ├── i18n/                 # 国际化配置
│   ├── layout/               # 布局组件
│   ├── model/                # 数据模型
│   ├── router/               # 路由配置
│   ├── stores/               # 状态管理
│   ├── theme/                # 主题样式
│   ├── types/                # TypeScript类型定义
│   ├── utils/                # 工具函数
│   └── views/                # 页面组件
│       ├── overview/         # 概览页面
│       ├── resourcePool/     # 资源池管理
│       ├── storagePool/      # 存储池管理
│       ├── networkManage/    # 网络管理
│       ├── monitoring/       # 监控告警
│       ├── system/           # 系统管理
│       ├── login/            # 登录页
│       ├── vnc/              # VNC远程连接
│       └── error/            # 错误页面
├── package.json              # 依赖配置
├── vite.config.ts            # Vite配置
└── tsconfig.json             # TypeScript配置
```

## 🎯 核心功能

### 🏠 概览
- **系统状态总览**: 物理机、虚拟机、存储总量统计
- **资源使用率**: CPU使用率/分配比、内存使用率/分配比、存储使用率展示
- **告警模块**: 实时告警信息展示和处理

### 💻 资源池管理
- **集群节点**: 集群配置和节点管理
- **主机节点**: 物理主机的监控和管理
- **虚拟机节点**: 虚拟机的创建、配置、生命周期管理
- **资源节点**: 计算资源的分配和调度

### 💾 存储管理
- **存储池**: 存储池的创建、配置和管理
- **存储访问**: 存储访问权限和路径管理
- **镜像管理**: 虚拟机镜像的上传、管理和部署

### 🌐 网络管理
- **网络拓扑**: 网络架构可视化展示
- **分布式交换机**: 虚拟交换机配置和管理
- **防火墙**: 网络安全策略配置
- **安全组**: 虚拟机安全组规则管理

### 📊 监控告警
- **物理机监控**: 物理服务器性能监控
- **虚拟机监控**: 虚拟机资源使用监控
- **存储监控**: 存储系统性能和容量监控
- **告警管理**: 告警规则配置和告警处理
- **告警规则**: 自定义告警阈值和规则

### ⚙️ 系统功能
- **用户管理**: 系统用户的增删改查
- **授权管理**: 用户权限和角色配置
- **日志管理**: 系统操作日志查看和分析
- **密码修改**: 用户密码安全管理
- **安全配置**: 系统安全策略配置
- **系统管理**: 系统参数和配置管理

### 🖥️ 远程管理
- **VNC连接**: 虚拟机远程桌面访问

## 🛠️ 安装与运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 调试模式
npm run debug
```

访问: http://localhost:5173

### 生产构建
```bash
# 构建生产版本（包含类型检查）
npm run build

# 快速构建（跳过类型检查）
npm run build:no-check
```

### 代码检查
```bash
# ESLint检查
npm run lint

# ESLint自动修复
npm run lint:fix

# TypeScript类型检查
npm run type-check
```

## 🔧 配置说明

### 环境变量
项目支持多环境配置，通过不同的 `.env` 文件管理：

- `.env` - 基础配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### 路由配置
路由采用前端控制模式，支持：
- 动态路由加载
- 权限验证
- 路由懒加载
- 面包屑导航

### 主题配置
支持多主题切换：
- 默认主题
- 深色主题
- 自定义主题配置

## 🌐 国际化

支持多语言切换：
- 简体中文 (zh-cn)
- 繁体中文 (zh-tw)  
- 英语 (en)

## 🔐 权限管理

基于模块化的权限控制系统：
- **概览**: 系统概览页面访问权限
- **资源池**: 资源池管理权限
- **存储管理**: 存储相关功能权限
- **网络管理**: 网络配置和管理权限
- **监控告警**: 监控和告警功能权限
- **系统功能**: 系统管理和配置权限

支持细粒度权限控制，包括路由级权限和功能级权限控制。

## 📱 响应式设计

完全响应式设计，支持：
- 桌面端 (>= 1200px)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🎨 UI特性

- 现代化Material Design风格
- 平滑的动画过渡效果
- 灵活的布局系统
- 丰富的图表组件
- 可自定义的主题系统

## 🔄 状态管理

使用Pinia进行状态管理：
- 用户信息和权限管理
- 主题配置管理
- 路由状态管理
- 标签页管理
- 系统配置管理

## 📊 数据可视化

集成ECharts图表库，支持：
- 折线图、柱状图、饼图、环形图
- 3D图表和GL渲染（ECharts GL）
- 词云图表（ECharts WordCloud）
- 液体填充图（ECharts LiquidFill）
- 实时数据更新和监控
- 交互式图表操作
- 自定义图表主题
- 网络拓扑图可视化

## 🧪 开发规范

### 代码规范
- 使用TypeScript严格模式
- 遵循ESLint + Prettier规范
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 🚀 特色功能

- **VNC远程连接**: 支持通过Web界面直接连接虚拟机桌面
- **Monaco代码编辑器**: 内置专业代码编辑器，支持语法高亮
- **拖拽式操作**: 支持拖拽排序和布局调整
- **实时监控**: 实时资源监控和性能数据展示
- **多语言支持**: 支持中文简体、繁体和英文
- **响应式设计**: 完美适配桌面端、平板端和移动端

## 📞 技术支持

如有问题或建议，请联系开发团队。

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](./LICENSE) 文件。

---

**开发团队** | **版本** v1.0.1 | **最后更新** 2025年