<template>
  <div class="tree-transfer">
    <div class="transfer-panel left-panel">
      <div class="panel-header">
        <span>{{ leftTitle || '待选项' }}</span>
      </div>
      <div class="panel-body">
        <el-tree
          ref="leftTree"
          :data="fromData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          @check-change="handleCheckChange"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span>
                <el-icon v-if="!isLeaf(data)"><folder /></el-icon>
                <el-icon v-else :class="{ 'disabled-icon': data.disabled }"><document /></el-icon>
                <span :class="{ 'disabled-label': data.disabled }">{{ node.label }}</span>
              </span>
              <span v-if="data.description" class="text-gray-400 text-xs ml-2">({{ data.description }})</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    <div class="transfer-buttons">
      <el-button type="primary" @click="addToRight" :disabled="!hasCheckedNodes">
        <el-icon><arrow-right /></el-icon>
      </el-button>
      <el-button type="primary" @click="removeFromRight" :disabled="!hasRightCheckedNodes">
        <el-icon><arrow-left /></el-icon>
      </el-button>
    </div>
    
    <div class="transfer-panel right-panel">
      <div class="panel-header">
        <span>{{ rightTitle || '已选项' }}</span>
      </div>
      <div class="panel-body">
        <el-tree
          ref="rightTree"
          :data="toData"
          show-checkbox
          node-key="id"
          :props="treeProps"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span>
                <el-icon v-if="!isLeaf(data)"><folder /></el-icon>
                <el-icon v-else><document /></el-icon>
                <span :class="{ 'disabled-label': data.disabled }">{{ node.label }}</span>
              </span>
              <span v-if="data.description" class="text-gray-400 text-xs ml-2">({{ data.description }})</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Folder, Document, ArrowRight, ArrowLeft } from '@element-plus/icons-vue'

const props = defineProps({
  fromData: {
    type: Array,
    default: () => []
  },
  toData: {
    type: Array,
    default: () => []
  },
  treeProps: {
    type: Object,
    default: () => ({
      label: 'label',
      children: 'children',
      disabled: 'disabled'
    })
  },
  leftTitle: {
    type: String,
    default: '待选项'
  },
  rightTitle: {
    type: String,
    default: '已选项'
  }
})

const emit = defineEmits(['update:fromData', 'update:toData', 'change'])

const leftTree = ref(null)
const rightTree = ref(null)

const hasCheckedNodes = computed(() => {
  return leftTree.value && leftTree.value.getCheckedKeys().length > 0
})

const hasRightCheckedNodes = computed(() => {
  return rightTree.value && rightTree.value.getCheckedKeys().length > 0
})

const isLeaf = (data) => {
  return !data.children || data.children.length === 0
}

  // 处理节点选中状态变化
  const handleCheckChange = () => {
    // 节点选中状态变化处理
  }

// 添加到右侧
const addToRight = () => {
  if (!leftTree.value) return
  
  // 获取选中的节点
  const checkedNodes = leftTree.value.getCheckedNodes()
  
  // 将选中的节点添加到右侧
  const newToData = [...props.toData, ...checkedNodes]
  emit('update:toData', newToData)
  
  // 清除左侧选中状态
  leftTree.value.setCheckedKeys([])
  
  // 触发变更事件
  emit('change', { direction: 'right', nodes: checkedNodes })
}

// 从右侧移除
const removeFromRight = () => {
  if (!rightTree.value) return
  
  // 获取右侧选中的节点ID
  const checkedKeys = rightTree.value.getCheckedKeys()
  
  // 过滤掉选中的节点
  const newToData = props.toData.filter(node => !checkedKeys.includes(node.id))
  emit('update:toData', newToData)
  
  // 清除右侧选中状态
  rightTree.value.setCheckedKeys([])
  
  // 触发变更事件
  emit('change', { direction: 'left', keys: checkedKeys })
}
</script>

<style scoped lang="scss">
@use '../../assets/scss/custom.scss' as *;
</style> 