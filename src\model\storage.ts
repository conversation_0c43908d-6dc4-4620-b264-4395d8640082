// 磁盘 表列
const diskColumns : MyTableColumns[] = [
	{ type: 'selection', wrap: true },
	{ label: '磁盘名称', prop: 'name', sortable: true, align: 'left' },
	{ label: '类型', prop: 'type_code', align: 'center' },
	{ label: '分配容量', tdSlot: 'capacity', align: 'center' },
	{ label: '使用容量', tdSlot: 'allocation', align: 'center' },
	{ label: '配置模式', prop: 'preallocation', align: 'center' },
	{ label: '路径', prop: 'path', align: 'center' },
	{ label: '创建时间', prop: 'time', align: 'center' },
	// { label: '备注', prop: 'remark', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 添加虚拟机 存储池 表列
const storageVMColumns : MyTableColumns[] = [
	{ label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '存储池', prop: 'name', sortable: true, align: 'center' },
	{ label: '已用', tdSlot: 'allocation', align: 'center' },
	{ label: '总量', tdSlot: 'capacity', align: 'center' },
	{ label: '存储地址', prop: 'storage_local_dir', align: 'center' },
	{ label: '时间', prop: 'time', align: 'center' },
	{ label: '类型', prop: 'type_code_display', align: 'center' },
];
// 添加虚拟机 磁盘 表列
const diskVMColumns : MyTableColumns[] = [
	{ label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '磁盘名称', prop: 'name', sortable: true, align: 'center' },
	{ label: '已使用', tdSlot: 'allocation', align: 'center' },
	{ label: '总量', tdSlot: 'capacity', align: 'center' },
];
// 字节+单位转换
const byteUnitConversion = (size: any) => {
	if (size == undefined) {
		return '-';
	}
	const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	let unitIndex = 0;
	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}
	return Math.floor(size * 10) / 10 + ' ' + units[unitIndex];
};
export { diskColumns, storageVMColumns, diskVMColumns, byteUnitConversion };
