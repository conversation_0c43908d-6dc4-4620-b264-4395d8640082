const fs = require('fs');
const path = require('path');

// 递归查找所有 .vue 文件
function findVueFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findVueFiles(filePath));
      }
    } else {
      const ext = path.extname(file);
      if (ext === '.vue') {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的类型问题
function fixTypesInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复 ziyuanchi 属性不存在的问题
    if (content.includes('powerItem.ziyuanchi')) {
      // 这个属性在类型定义中不存在，注释掉或移除
      content = content.replace(/\|\|\s*powerItem\.ziyuanchi/g, '');
      content = content.replace(/powerItem\.ziyuanchi\s*\|\|/g, '');
      content = content.replace(/powerItem\.ziyuanchi/g, 'false');
      modified = true;
    }
    
    // 修复 label/lable 拼写错误
    if (content.includes('item.label') && content.includes('lable:')) {
      content = content.replace(/item\.label/g, 'item.lable');
      modified = true;
    }
    
    // 修复 conversionTime 方法不存在的问题
    if (content.includes('conversionTime(')) {
      // 添加导入或定义
      if (!content.includes('import') || !content.includes('conversionTime')) {
        // 简单替换为格式化函数
        content = content.replace(/conversionTime\(([^)]+)\)/g, 'new Date($1).toLocaleString()');
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed types in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding Vue files to fix...');
  
  const files = findVueFiles(srcDir);
  console.log(`Found ${files.length} Vue files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixTypesInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed types in ${fixedCount} files`);
}

main();
