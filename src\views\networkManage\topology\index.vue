<template>
	<div class="topology-area layout-padding">
    <el-card>
      <div class="topology-header">
        <div class="topology-title">网络拓扑图</div>
        <div class="topology-actions">
          <el-button type="primary" @click="resetTopology">重置拓扑</el-button>
          <el-button type="success" @click="addNode">添加节点</el-button>
          <el-button type="warning" @click="toggleLinearMode">{{ state.linearMode ? '切换到自由模式' : '切换到线性模式' }}</el-button>
        </div>
      </div>
      <div class="topology-container" ref="topologyContainer">
        <!-- 节点面板 -->
        <div class="node-panel" v-if="!state.linearMode">
          <div class="node-item router" @mousedown="startDrag($event, 'router')">
            <div class="node-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="node-label">路由器</div>
          </div>
          <div class="node-item switch" @mousedown="startDrag($event, 'switch')">
            <div class="node-icon">
              <el-icon><SwitchButton /></el-icon>
            </div>
            <div class="node-label">交换机</div>
          </div>
          <div class="node-item server" @mousedown="startDrag($event, 'server')">
            <div class="node-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="node-label">服务器</div>
          </div>
          <div class="node-item client" @mousedown="startDrag($event, 'client')">
            <div class="node-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="node-label">客户端</div>
          </div>
        </div>

        <!-- 拓扑图区域 -->
        <div class="topology-canvas" ref="topologyCanvas" @mouseup="stopDrag" @mousemove="onDrag">
          <!-- 连接线 -->
          <svg class="topology-svg" ref="topologySvg">
            <g>
              <template v-for="(link, index) in state.links" :key="'link-' + index">
                <line
                  :x1="link.source.x + link.source.width/2"
                  :y1="link.source.y + link.source.height/2"
                  :x2="link.target.x + link.target.width/2"
                  :y2="link.target.y + link.target.height/2"
                  stroke="#666"
                  stroke-width="2"
                  :class="{ 'link-active': link.active }"
                ></line>
                <!-- 连接线上的标签 -->
                <text
                  :x="(link.source.x + link.target.x + link.source.width/2 + link.target.width/2) / 2"
                  :y="(link.source.y + link.target.y + link.source.height/2 + link.target.height/2) / 2 - 5"
                  text-anchor="middle"
                  fill="#333"
                  font-size="12"
                >{{ link.label || '' }}</text>
              </template>
            </g>
          </svg>

          <!-- 节点 -->
          <div
            v-for="(node, index) in state.nodes"
            :key="'node-' + index"
            class="topology-node"
            :class="[node.type, { 'node-active': node.active }]"
            :style="{ left: node.x + 'px', top: node.y + 'px' }"
            @mousedown="selectNode($event, node)"
            @dblclick="editNode(node)"
          >
            <div class="node-icon">
              <el-icon v-if="node.type === 'router'"><Connection /></el-icon>
              <el-icon v-else-if="node.type === 'switch'"><SwitchButton /></el-icon>
              <el-icon v-else-if="node.type === 'server'"><Monitor /></el-icon>
              <el-icon v-else-if="node.type === 'client'"><Cpu /></el-icon>
            </div>
            <div class="node-label">{{ node.label }}</div>
            <div class="node-status" :class="node.status">{{ getStatusText(node.status) }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 节点修改对话框 -->
    <el-dialog v-model="state.dialogVisible" title="修改节点" append-to-body class="dialog-500">
      <el-form :model="state.currentNode" label-width="80px">
        <el-form-item label="名称">
          <el-input v-model="state.currentNode.label"></el-input>
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input v-model="state.currentNode.ip"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="state.currentNode.status" style="width: 100%">
            <el-option label="在线" value="online"></el-option>
            <el-option label="离线" value="offline"></el-option>
            <el-option label="警告" value="warning"></el-option>
            <el-option label="错误" value="error"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input type="textarea" v-model="state.currentNode.description" rows="3"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveNode">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch, computed } from 'vue';
import { Connection, SwitchButton, Monitor, Cpu } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

const router = useRouter();
const topologyContainer = ref<HTMLElement | null>(null);
const topologyCanvas = ref<HTMLElement | null>(null);
const topologySvg = ref<SVGElement | null>(null);

// 定义节点类型接口
interface Node {
  id: string;
  type: string;
  label: string;
  x: number;
  y: number;
  width: number;
  height: number;
  ip?: string;
  status: string;
  description?: string;
  active: boolean;
}

// 定义连接线接口
interface Link {
  id: string;
  source: Node;
  target: Node;
  label?: string;
  active: boolean;
}

// 定义状态
const state = reactive({
  nodes: [] as Node[],
  links: [] as Link[],
  dragging: false,
  dragNode: null as Node | null,
  dragType: '',
  dragOffsetX: 0,
  dragOffsetY: 0,
  selectedNode: null as Node | null,
  currentNode: {} as Node,
  dialogVisible: false,
  linearMode: true,
  nodeCounter: 0
});

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online': return '在线';
    case 'offline': return '离线';
    case 'warning': return '警告';
    case 'error': return '错误';
    default: return '未知';
  }
};

// 初始化拓扑图
const initTopology = () => {
  // 清空现有节点和连接
  state.nodes = [];
  state.links = [];
  state.nodeCounter = 0;

  if (state.linearMode) {
    // 线性拓扑模式 - 创建一个简单的线性网络
    createLinearTopology();
  } else {
    // 自由模式 - 可以从面板拖拽添加节点
  }
};

// 创建线性拓扑
const createLinearTopology = () => {
  if (!topologyCanvas.value) return;

  const canvasWidth = topologyCanvas.value.clientWidth;
  const canvasHeight = topologyCanvas.value.clientHeight;
  const nodeHeight = 80;
  const startX = 100;
  const startY = canvasHeight / 2 - nodeHeight / 2;
  const spacing = 150;

  // 创建路由器节点
  const routerNode = createNode('router', 'Router-1', startX, startY);

  // 创建交换机节点
  const switch1 = createNode('switch', 'Switch-1', startX + spacing, startY);

  // 创建服务器节点
  const server1 = createNode('server', 'Server-1', startX + spacing * 2, startY);
  const server2 = createNode('server', 'Server-2', startX + spacing * 3, startY);

  // 创建客户端节点
  const client1 = createNode('client', 'Client-1', startX + spacing * 4, startY);

  // 创建连接
  createLink(routerNode, switch1, '1Gbps');
  createLink(switch1, server1, '1Gbps');
  createLink(switch1, server2, '1Gbps');
  createLink(server2, client1, '1Gbps');
};

// 创建节点
const createNode = (type: string, label: string, x: number, y: number): Node => {
  const node: Node = {
    id: `node-${++state.nodeCounter}`,
    type,
    label,
    x,
    y,
    width: 80,
    height: 80,
    status: 'online',
    active: false,
    ip: `192.168.1.${state.nodeCounter}`,
    description: `这是一个${type}节点`
  };

  state.nodes.push(node);
  return node;
};

// 创建连接
const createLink = (source: Node, target: Node, label?: string): Link => {
  const link: Link = {
    id: `link-${source.id}-${target.id}`,
    source,
    target,
    label,
    active: false
  };

  state.links.push(link);
  return link;
};

// 重置拓扑
const resetTopology = () => {
  ElMessageBox.confirm('确定要重置拓扑图吗？所有自定义节点和连接将被删除。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    initTopology();
    ElMessage.success('拓扑图已重置');
  }).catch(() => {});
};

// 添加节点
const addNode = () => {
  if (!topologyCanvas.value) return;

  // 在画布中心添加一个新节点
  const canvasWidth = topologyCanvas.value.clientWidth;
  const canvasHeight = topologyCanvas.value.clientHeight;

  const nodeTypes = ['router', 'switch', 'server', 'client'];
  const randomType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];

  const node = createNode(
    randomType,
    `${randomType.charAt(0).toUpperCase() + randomType.slice(1)}-${state.nodeCounter}`,
    canvasWidth / 2 - 40,
    canvasHeight / 2 - 40
  );

  // 如果有选中的节点，则创建连接
  if (state.selectedNode) {
    createLink(state.selectedNode, node);
  }

  // 选中新节点
  selectNode(null, node);
};

// 切换线性模式
const toggleLinearMode = () => {
  state.linearMode = !state.linearMode;
  initTopology();
};

// 开始拖拽
const startDrag = (event: MouseEvent, type: string) => {
  if (!topologyCanvas.value) return;

  event.preventDefault();

  state.dragging = true;
  state.dragType = type;

  // 记录鼠标相对于节点的偏移
  state.dragOffsetX = event.offsetX;
  state.dragOffsetY = event.offsetY;

  const canvasRect = topologyCanvas.value.getBoundingClientRect();

  // 创建一个新节点并开始拖拽
  const node = createNode(
    type,
    `${type.charAt(0).toUpperCase() + type.slice(1)}-${state.nodeCounter}`,
    event.clientX - canvasRect.left - state.dragOffsetX,
    event.clientY - canvasRect.top - state.dragOffsetY
  );

  state.dragNode = node;

  // 添加鼠标移动和松开事件
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
};

// 拖拽中
const onDrag = (event: MouseEvent) => {
  if (!state.dragging || !state.dragNode || !topologyCanvas.value) return;

  const canvasRect = topologyCanvas.value.getBoundingClientRect();

  // 计算新位置，确保节点不会超出画布
  let newX = event.clientX - canvasRect.left - state.dragOffsetX;
  let newY = event.clientY - canvasRect.top - state.dragOffsetY;

  // 边界检查
  newX = Math.max(0, Math.min(newX, canvasRect.width - state.dragNode.width));
  newY = Math.max(0, Math.min(newY, canvasRect.height - state.dragNode.height));

  // 更新节点位置
  state.dragNode.x = newX;
  state.dragNode.y = newY;
};

// 停止拖拽
const stopDrag = () => {
  state.dragging = false;
  state.dragNode = null;

  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 选择节点
const selectNode = (event: MouseEvent | null, node: Node) => {
  // 如果是拖拽开始，记录偏移
  if (event) {
    event.stopPropagation();
    state.dragging = true;
    state.dragNode = node;
    state.dragOffsetX = event.offsetX;
    state.dragOffsetY = event.offsetY;
  }

  // 取消所有节点和连接的选中状态
  state.nodes.forEach(n => n.active = false);
  state.links.forEach(l => l.active = false);

  // 设置当前节点为选中状态
  node.active = true;
  state.selectedNode = node;

  // 高亮与该节点相关的连接
  state.links.forEach(link => {
    if (link.source === node || link.target === node) {
      link.active = true;
    }
  });
};

// 修改节点
const editNode = (node: Node) => {
  state.currentNode = { ...node };
  state.dialogVisible = true;
};

// 保存节点
const saveNode = () => {
  // 找到要更新的节点
  const node = state.nodes.find(n => n.id === state.currentNode.id);
  if (node) {
    // 更新节点属性
    node.label = state.currentNode.label;
    node.ip = state.currentNode.ip;
    node.status = state.currentNode.status;
    node.description = state.currentNode.description;
  }

  state.dialogVisible = false;
  ElMessage.success('节点信息已更新');
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initTopology();
  });
});
</script>
<style scoped lang="scss">
.topology-area {
  padding-top: 0 !important;
	width: calc(100%);
	height: calc(100%);
}

.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
	}
}

.topology-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .topology-title {
    font-size: 18px;
    font-weight: bold;
  }

  .topology-actions {
    display: flex;
    gap: 10px;
  }
}

.topology-container {
  display: flex;
  flex: 1;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  height: calc(100% - 50px);
}

.node-panel {
  width: 120px;
  background-color: #f5f7fa;
  border-right: 1px solid #ddd;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .node-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: grab;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #e6f1fc;
    }

    .node-icon {
      font-size: 24px;
      margin-bottom: 5px;
    }

    .node-label {
      font-size: 12px;
    }

    &.router .node-icon {
      color: #409eff;
    }

    &.switch .node-icon {
      color: #67c23a;
    }

    &.server .node-icon {
      color: #e6a23c;
    }

    &.client .node-icon {
      color: #f56c6c;
    }
  }
}

.topology-canvas {
  flex: 1;
  position: relative;
  background-color: #fff;
  overflow: hidden;
}

.topology-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  line {
    transition: all 0.3s;

    &.link-active {
      stroke: #409eff;
      stroke-width: 3;
    }
  }
}

.topology-node {
  position: absolute;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 5px;
  cursor: move;
  user-select: none;
  transition: all 0.3s;
  z-index: 2;

  .node-icon {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .node-label {
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .node-status {
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 10px;
    margin-top: 3px;

    &.online {
      background-color: #f0f9eb;
      color: #67c23a;
    }

    &.offline {
      background-color: #f4f4f5;
      color: #909399;
    }

    &.warning {
      background-color: #fdf6ec;
      color: #e6a23c;
    }

    &.error {
      background-color: #fef0f0;
      color: #f56c6c;
    }
  }

  &.router {
    border-color: #409eff;

    .node-icon {
      color: #409eff;
    }
  }

  &.switch {
    border-color: #67c23a;

    .node-icon {
      color: #67c23a;
    }
  }

  &.server {
    border-color: #e6a23c;

    .node-icon {
      color: #e6a23c;
    }
  }

  &.client {
    border-color: #f56c6c;

    .node-icon {
      color: #f56c6c;
    }
  }

  &.node-active {
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
    transform: scale(1.05);
    z-index: 3;
  }
}
</style>