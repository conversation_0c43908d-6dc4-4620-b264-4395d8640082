<template>
	<div class="task-statistics">
		<my-echarts :options="state.chartOption"></my-echarts>
	</div>
</template>
<script setup lang="ts" name="EchartsRate">
import { defineAsyncComponent, reactive, onMounted, ref, Ref, inject, watch } from 'vue';
const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));
import * as echarts from 'echarts';

const props = defineProps({
	read: {
		type: Object,
		required: true,
	},
});
const getOption = () => {
	return {
		backgroundColor: '#fff',
		/** 标题*/
		title: [
			{
				text: '{val|' + props.read.value + '}',
				bottom: '40%',
				left: 'center',
				textStyle: {
					rich: {
						val: {
							fontSize: '20',
							color: '#150900',
						},
					},
				},
				triggerEvent: true,
			},
			{
				text: '{name|' + props.read.title + '}',
				bottom: '0%',
				left: 'center',
				textStyle: {
					rich: {
						name: {
							fontSize: '14',
							color: '#333',
						},
					},
				},
				triggerEvent: true,
			},
		],

		/** 关闭必图例*/
		legend: {
			show: false,
		},
		series: [
			{
				name: '最外部进度条',
				type: 'gauge',
				radius: '75%',
				splitNumber: 4,
				axisLine: {
					lineStyle: {
						color: [[1, '#1a6de8']],
						width: 1,
					},
				},
				axisLabel: {
					show: false,
					distance: -58,
					textStyle: {
						color: '#000',
						fontSize: '20',
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				itemStyle: {
					show: false,
				},
				detail: {
					show: false,
				},
				title: {
					// 标题
					show: false,
				},
				data: [
					{
						name: 'title',
						value: props.read,
					},
				],
				pointer: {
					show: false,
				},
			},

			{
				name: '刻度尺',
				type: 'gauge',
				radius: '65%',
				splitNumber: 3, // 刻度数量
				min: 0, // 最小刻度
				max: 100, // 最大刻度
				// 仪表盘轴线相关配置
				axisLine: {
					lineStyle: {
						color: [[1, '#1e6bf7']],
						width: 10,
					},
				},
				/** 分隔线样式*/
				splitLine: {
					show: false,
				},
				/** 刻度线*/
				axisTick: {
					show: false,
				},
				/** 刻度标签*/
				axisLabel: {
					show: false,
				},
				detail: {
					show: false,
				},
			},
			{
				name: '内层带指针',
				type: 'gauge',
				radius: '60%',
				splitNumber: 0, // 刻度数量
				min: 0, // 最小刻度
				max: 100, // 最大刻度
				// 仪表盘轴线相关配置
				axisLine: {
					lineStyle: {
						color: [
							[
								1,
								{
									type: 'radial',
									x: 0.5,
									y: 0.59,
									r: 0.6,
									colorStops: [
										{
											offset: 0.72,
											color: '#70b4f7',
										},
										{
											offset: 0.98,
											color: '#4bafcc',
										},
										{
											offset: 1,
											color: '#ccc',
										},
									],
								},
							],
						],
						width: 30,
					},
				},
				/** 分隔线样式*/
				splitLine: {
					show: false,
				},
				/** 刻度线*/
				axisTick: {
					show: false,
				},
				/** 刻度标签*/
				axisLabel: {
					show: false,
				},
				/** 仪表盘指针*/
				pointer: {
					show: false,
					length: '80%',
					width: 5, // 指针粗细
				},
				/** 仪表盘指针样式*/
				itemStyle: {
					color: '#333',
				},
				data: [
					{
						value: props.read,
					},
				],
				detail: {
					show: false,
				},
			},
		],
		graphic: {
			elements: [
				{
					type: 'line',
					z: 4,
					style: {
						fill: '#075173',
						stroke: '#075173',
						lineWidth: 2,
						shadowBlur: 15,
						shadowOffsetX: 0,
						shadowOffsetY: -4,
						shadowColor: '#13E6FF',
					},
					left: 'center',
					bottom: '21%',
					silent: true,
				},
				{
					type: 'line',
					z: 4,
					style: {
						fill: '#075173',
						stroke: '#075173',
						lineWidth: 2,
						shadowBlur: 15,
						shadowOffsetX: 0,
						shadowOffsetY: -4,
						shadowColor: '#13E6FF',
					},
					left: 'center',
					bottom: '28.5%',
					silent: true,
				},
			],
		},
	};
};
// 定义变量内容
const state = reactive<EmptyObjectType>({
	chartOption: getOption(),
});

// 页面加载时
onMounted(() => {
	window.addEventListener('resize', () => {
		state.chartOption = getOption();
	});
});
watch(
	() => props.read,
	(val) => {
		state.chartOption = getOption();
	}
);
</script>
<style scoped lang="scss">
.task-statistics {
	height: calc(100%);
}
</style>
