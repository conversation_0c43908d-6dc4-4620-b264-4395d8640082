<template>
	<div class="monitoring-area layout-padding">
    <el-card>
      <div class="storage-monitor-area">
				<!-- 存储基本数据 -->
				<h1>集群管理</h1>
				<div class="basic-area">
					<div class="basic-alarm">
						<el-card>
							<h4>警报</h4>
							<div class="alarm-content">
								<img src="../../assets/images/cc-alarm.png">
								<div class="alarm-list">
									<span>警报数量</span>
									<h1>{{ state.storageAlert }}</h1>
								</div>
							</div>
						</el-card>
					</div>
					<div class="basic-storage">
						<el-card>
							<h4>存储容量</h4>
							<div class="storage-content">
								<div class="storage-chart">
									<EchartCircle :storageRate="state.storageRate"></EchartCircle>
								</div>
								<div class="storage-list">
									<div class="storage-number">
										<span style="background: #6d3df1"></span>
										<span>已使用</span>
										<span>{{ translation(state.storageUsed) }}</span>
									</div>
									<div class="storage-number">
										<span style="background: #ccc"></span>
										<span>未使用</span>
										<span>{{ translation(state.storageNoUsed) }}</span>
									</div>
									<div class="storage-number">
										<span></span>
										<span>共计</span>
										<span>{{ translation(state.storageTotal) }}</span>
									</div>
								</div>
							</div>
						</el-card>
					</div>
					<div class="basic-dashboard">
						<el-card>
							<h4>存储健康状态</h4>
							<div class="dashboard-echart">
								<EchartHealth :healthState="state.healthState"></EchartHealth>
							</div>
						</el-card>
					</div>
					<div class="basic-dashboard">
						<el-card>
							<h4>IOPS</h4>
							<div class="dashboard-rate">
								<div class="echart-rate">
									<EchartRate :read="state.ioRead"></EchartRate>
								</div>
								<div class="echart-rate">
									<EchartRate :read="state.ioWrite"></EchartRate>
								</div>
							</div>
						</el-card>
					</div>
				</div>
				<!-- 节点详情数据 -->
				<div class="node-area">
					<el-card>
					<div class="chart-radio">
            <div class="radio-chart-piece" v-for="item in state.nodeList" :key="item.lable" @click="radioClick(item.lable)">
              <span class="radio-title" :style="{color:state.nodeItem==item.lable?'#121529':'#717379'}">{{item.lable}}</span>
              <span class="radio-selected" :style="{background:state.nodeItem==item.lable?'#fe6902':''}"></span>
            </div>
          </div>
					<el-table stripe :data="state.nodeData" height='200'>
						<el-table-column prop="devid" label="磁盘序列号" />
						<el-table-column prop="device_class" label="接口类型" />
						<el-table-column prop="osd_stats" label="磁盘总容量">
							<template #default="{row}">
								<span>{{ row.osd_stats.kb }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="kb_used" label="OSD已使用容量">
							<template #default="{row}">
								<span>{{ row.osd_stats.kb_used }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="status" label="磁盘状态" width='100'>
							<template #default="{row}">
								<span :style="{color: row.status=='up'?'green':'red'}">{{ row.status=='up'?'正常':'异常' }}</span>
							</template>
						</el-table-column>
						<template #empty>
          		<img src="../../assets/images/no-data.svg" alt="" class="minimg-wrap" />
						</template>
					</el-table>
					</el-card>
				</div>
				<h1>资源统计</h1>
				<div class="statistics-area">
					<div class="statistics-item" v-for="(item,index) in state.clusterData" :key="index">
						<el-card>
							<h4>{{ item.title }}</h4>
							<div class="statistics-echart">
								<div class="chart-no-datas" v-if="!item.chartShwo">
									<img src="../../assets/images/no-data.svg" >
								</div>
								<EchartLine :chartData="item.datas" :time="1" v-if="item.chartShwo"></EchartLine>
							</div>
						</el-card>
					</div>
				</div>
			</div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
	import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
	import cluster from 'cluster';
	import { selectTime,translation } from '/@/model/monitoring'; // 表列、正则
	import { storageBasicQuery,storageNodeQuery,storageClusterStorage,storageClusterMemory,storageClusterIOPS,storageClusterHandlingCapacity } from '/@/api/Monitoring'; // 接口
	import { AnyCnameRecord } from 'dns';
	
	const EchartCircle = defineAsyncComponent(() => import('./echartPublic/EchartCircle.vue'));
	const EchartHealth = defineAsyncComponent(() => import('./echartPublic/EchartHealth.vue'));
	const EchartRate = defineAsyncComponent(() => import('./echartPublic/EchartRate.vue'));
	const EchartLine = defineAsyncComponent(() => import('./echartPublic/EchartLine.vue'));
	// 定义变量内容
	const state = reactive({
		storageAlert: 0, // 告警
		storageUsed: 0, // 已用
		storageNoUsed: 0, // 未用
		storageTotal: 0, // 总量
		storageRate: 0, // 使用率
		healthState: '正常', // 健康状态
		ioRead: {
			title: "IOPS读",
			value:  0,
		},
		ioWrite: {
			title: "IOPS写",
			value:  0,
		},
		nodeList: [
			{ lable:'节点1' },
			{ lable:'节点2' },
			{ lable:'节点3' },
		],
		nodeItem: '节点1',
		nodeColumn: [],
		nodeAllData: [],
		nodeData: [],
		clusterData: [
			{ title: "CPU平均使用率", datas: {}, chartShwo:false },
      { title: "平均内存使用量", datas: {}, chartShwo:false },
      { title: "IOPS", datas: {}, chartShwo:false },
      { title: "吞吐量", datas: {}, chartShwo:false },
		],
	});
	const radioClick=(item:any)=>{
		state.nodeItem = item
	}
	// 基本数据查询
	const basicData = ()=>{
		storageBasicQuery().then((res:any)=>{
			state.storageAlert = res.alertcounts
			state.storageUsed = res.used
			state.storageNoUsed = res.residue
			state.storageTotal = res.total
			state.storageRate = Math.floor((res.data.used / res.data.total) * 1000) / 10;
			state.healthState = res.status

		})
		storageNodeQuery().then((res:any)=>{
			state.nodeAllData = res
			state.nodeItem = res.osd_data
		})
	}
	// 获取集群数据
	const clusterQuery = ()=>{
		// 存储使用率
		state.clusterData[0].datas ={
			unit: 'B',
			data: [
				{title: '物理机1上行',list:[123456,734562,1,132456,574632]},
				{title: '物理机1下行',list:[null,102904,837491,122243,109]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.clusterData[0].chartShwo = true
		// storageClusterStorage({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
		// 	if(res?.length>0) {
		// 		state.clusterData[0].datas = res
		// 		state.clusterData[0].chartShwo = true
		// 	}else {
		// 		state.clusterData[0].chartShwo = false
		// 	}
		// })
		// 内存使用率
		state.clusterData[1].datas ={
			unit: '%',
			data: [
				{title: '物理机1',list:[10,20,5,30,1]},
      	{title: '物理机2',list:[50,10,30,1,70]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.clusterData[1].chartShwo = true
		// storageClusterMemory({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
		// 	if(res?.length>0) {
		// 		state.clusterData[1].datas = res
		// 		state.clusterData[1].chartShwo = true
		// 	}else {
		// 		state.clusterData[1].chartShwo = false
		// 	}
		// })
		// IOPS数据
		state.clusterData[2].datas ={
			unit: '%',
			data: [
				{title: '物理机1',list:[2,20,5,30,1]},
				{title: '物理机2',list:[5,10,5,1,7]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.clusterData[2].chartShwo = true
		// storageClusterIOPS({start:selectTime(24),end:selectTime(0)}).then(res=>{
		// 	if(res?.length>0) {
		// 		state.clusterData[2].datas = res
		// 		state.clusterData[2].chartShwo = true
		// 	}else {
		// 		state.clusterData[2].chartShwo = false
		// 	}
		// 	res?.data.forEach((item:any)=>{
		// 		if(item.title=='IOPS读'){
		// 			state.ioRead.value = item.list[item.list.length-1]*-1
		// 			state.ioRead.title = item.title
		// 		}else if(item.title=='IOPS写'){
		// 			state.ioRead.value = item.list[item.list.length-1]
		// 			state.ioRead.title = item.title
		// 		}
		// 	})
		// })
		// 吞吐量
		state.clusterData[3].datas ={
			unit: '%',
			data: [
				{title: '物理机1',list:[3,7,51,34,12]},
				{title: '物理机2',list:[-5,-1,-33,-10,-27]},
			],
			time: ['1732672800000','1732673100000','1732673400000','1732673700000','1732674000000'],
		}
		state.clusterData[3].chartShwo = true
		// storageClusterHandlingCapacity({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
		// 	if(res?.length>0) {
		// 		state.clusterData[3].datas = res
		// 		state.clusterData[3].chartShwo = true
		// 	}else {
		// 		state.clusterData[3].chartShwo = false
		// 	}
		// })
	}
	// 页面加载时
	onMounted(() => {
		basicData()
		clusterQuery()
	});
</script>

<style scoped lang="scss">
	.monitoring-area {
		padding-top: 0 !important;
    width: 100%;
    height: 100%;
		.storage-monitor-area {
			width: 100%;
			height: 100%;
			overflow: auto;
			.basic-area {
				width: 100%;
				height: 250px;
				margin-bottom: 15px;
				display: flex;
    		justify-content: space-between;
				.basic-alarm {
					width: 330px;
					height: 100%;
					.alarm-content {
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: space-evenly;
						img {
							width: 122px;
							height: 133px;
						}
						.alarm-list {
							width: 100px;
							height: 100px;
							span {
								display: inline-block;
								margin-bottom: 20px;
							}
							h1 {
								font-size: 30px;
							}
						}
					}
				}
				.basic-storage {
					width: 420px;
					height: 100%;
					.storage-content {
						width: 100%;
						height: calc(100% - 20px);
						display: flex;
						align-items: center;
						justify-content: space-around;
						.storage-chart {
							width: 180px;
							height: 180px;
						}
						.storage-list {
							.storage-number {
								width: 130px;
								height: 40px;
								span:nth-child(1) {
									display: inline-block;
									width: 10px;
									height: 10px;
									border-radius: 3px;
								}
								span:nth-child(2) {
									display: inline-block;
									width: 45px;
									text-align: right;
								}
								span:nth-child(3) {
									display: inline-block;
									width: 70px;
									text-align: right;
								}
							}
						}
					}
				}
				.basic-dashboard {
					width: 420px;
					height: 100%;
					.dashboard-echart {
						width: 100%;
						height: calc(100% - 20px);
					}
					.dashboard-rate {
						width: 100%;
						height: calc(100% - 20px);
						display: flex;
    				justify-content: space-between;
						.echart-rate {
							width: 48%;
							height: 100%;
						}
					}
				}
			}
			.node-area {
				width: 100%;
				margin-bottom: 15px;
				.chart-radio {
					width: 100%;
					height: 30px;
          display: flex;
          align-items: center;
          font-size: 15px;
          .radio-chart-piece {
            display: flex;
            align-content: space-between;
            flex-direction: column;
            align-items: center;
            margin-right: 26px;
            font-weight: 800;
            cursor: pointer;
            .radio-selected {
              width: 10px;
              height: 6px;
              border-radius: 3px;
            }
          }
        }
			}
			.statistics-area {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				.statistics-item {
					width: 49.5%;
					height: 400px;
					margin-bottom: 15px;

					.statistics-echart {
						width: 100%;
						height: calc(100% - 20px);
						.chart-no-datas {
							width: 100%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							img {
								height: 70%;
							}
						}
					}
				}
			}
		}
	}
  .el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
	h1 {
		margin-bottom: 15px;
	}
	.minimg-wrap {
		height: 100px;
		margin-top: 1%;
	}
	.no-datas-wrap {
		margin-top: -4%;
	}
}
:deep(.el-table__header) {
      .el-table__cell {
        height: 44px;
        background: var(--next-bg-main-color);
        color: var(--el-text-color-primary);
      }
    }
</style>