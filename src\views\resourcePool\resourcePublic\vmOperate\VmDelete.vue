<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否删除下列虚拟机？</span>
      <p style="color:red;word-wrap: break-word">{{ formItem.names.toString() }}</p>
      <div class="chec-area">
        <el-checkbox v-model="formItem.status" label="彻底删除" /> <span :style="{color: formItem.status?'#000':'#ccc'}">启用后将彻底删除虚拟机，否则将移入回收站。</span>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { recycleVmDelete,vmMoveinRecycle } from '/@/api/ResourcePool/vm'; // 接口
import { dayjs,ElMessage } from 'element-plus';

const formItem = reactive({
  isShow: false,
  status: false,
  title: '删除',
  names: [''],
  ids: [''],
  hostip: '',
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.isShow = false;
  if(formItem.status) {
    recycleVmDelete({
      host_ip: formItem.hostip,
      names: formItem.names,
      ids: formItem.ids,
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('彻底删除虚拟机操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    vmMoveinRecycle({
      host_ip: formItem.hostip,
      names: formItem.names,
      ids: formItem.ids,
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('删除虚拟机移入回收站操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }
}
// 打开弹窗
const openDialog = async (arr:any,treeItem:any) => {
  formItem.isShow = true;
  formItem.status = false;
  nextTick(() => {
    formItem.title = '删除虚拟机'
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formItem.names = names
    formItem.ids = ids
    formItem.hostip = treeItem.ip
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
  .chec-area {
    display: flex;
    align-items: center;
    padding: 15px 0;
    span {
      margin-left: 5px;
    }
    
  }
</style>