<template>
	<el-card>
		<div>
			<el-radio-group v-model="state.antivirus" @change="changeAntivirus">
				<el-radio border value="not"> 无 </el-radio>
				<el-radio border value="qita">第三方厂商</el-radio>
			</el-radio-group>
		</div>
	</el-card>
</template>
<script setup lang="ts" name="AutivirusConfig">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { antivirusConfigQuery, antivirusConfigEdit } from '/@/api/System'; // 接口
const state = reactive({
	antivirus: 'not',
});
// 防病毒 查询
const antivirusQuery = () => {
	antivirusConfigQuery().then((res) => {
		state.antivirus = res.antivirus_type;
	});
};
// 防病毒 修改
const changeAntivirus = () => {
	antivirusConfigEdit({ antivirus_type: 'not' }).then((res) => {
		ElMessage.success('防病毒配置切换完成');
		antivirusQuery();
	});
};
onMounted(() => {
	// antivirusQuery()
});
</script>