/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseDialog: typeof import('./components/BaseDialog/index.vue')['default']
    Chart: typeof import('./components/echarts/chart.vue')['default']
    Echarts: typeof import('./components/echarts/index.vue')['default']
    ExampleForm: typeof import('./components/ExampleForm/index.vue')['default']
    IconUpload: typeof import('./components/upLoader/IconUpload.vue')['default']
    ImageLoader: typeof import('./components/upLoader/ImageLoader.vue')['default']
    MyTable: typeof import('./components/table/MyTable.vue')['default']
    RouteNav: typeof import('./components/RouteNav/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./components/table/Search.vue')['default']
    SvgIcon: typeof import('./components/svgIcon/index.vue')['default']
    SvgSymbol: typeof import('./components/SvgSymbol/index.vue')['default']
    TableSearch: typeof import('./components/TableSearch/index.vue')['default']
    TheDialog: typeof import('./components/TheDialog/index.vue')['default']
    ThePagination: typeof import('./components/ThePagination/index.vue')['default']
    TheTable: typeof import('./components/TheTable/index.vue')['default']
    TheUpload: typeof import('./components/TheUpload/index.vue')['default']
    TimelineList: typeof import('./components/TimelineList/index.vue')['default']
    TreeFilter: typeof import('./components/TreeFilter/index.vue')['default']
    TreeTransfer: typeof import('./components/TreeTransfer/index.vue')['default']
  }
}
