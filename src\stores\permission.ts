import { defineStore } from 'pinia';
export const permissionStores = defineStore('permisInfo', {
	state: (): permisInfo => ({
		userName: '',
		role: '',
		ceshi: false,

		// 资源池
		tianjiazhujichi: false, // 添加主机池
		xiugaizhujichi: false, // 修改主机池
		shanchuzhujichi: false, // 删除主机池
		tianjiajiqun: false, // 添加集群
		xiugaijiqun: false, // 修改集群
		shanchujiqun: false, // 删除集群
		jiqunHA: false, // 集群HA
		jiqunDRS: false, // 集群DRS
		tianjiawuliji: false, // 添加物理机
		xiugaiwuliji: false, // 修改物理机
		shanchuwuliji: false, // 删除物理机
		guanbiwuliji: false, // 关闭物理机
		chongqiwuliji: false, // 重启物理机
		tuichuwuliji: false, // 退出物理机
		weihumoshi: false, // 维护模式
		tianjiaxuniji: false, // 添加虚拟机
		xunijikaiji: false, // 虚拟机开机
		xunijiguanji: false, // 虚拟机关机
		xunijichongqi: false, // 虚拟机重启
		xunijishanchu: false, // 虚拟机删除
		xunijiqiangzhichongqi: false, // 虚拟机强制重启
		xunijiguanbidianyuan: false, // 虚拟机关闭电源
		xunijizanting: false, // 虚拟机暂停
		xunijihuifu: false, // 虚拟机恢复
		xunijiwanquankelong: false, // 虚拟机完全克隆
		xunijilianjiekelong: false, // 虚拟机链接克隆
		xunijiqianyi: false, // 虚拟机迁移
		xunijikongzhitai: false, // 虚拟机控制台
		xunijixiugai: false, // 虚拟机修改
		ziyuanjiediangaiyao: false, // 资源节点概要
		zhujichigaiyao: false, // 主机池概要
		jiqungaiyao: false, // 集群概要
		zhujigaiyao: false, // 主机概要
		xunijigaiyao: false, // 虚拟机概要
		zhujichi: false,
		jiqun: false,
		wuliji: false,
		xuniji: false,
		cunchuchi: false,
		fenbushijiaohuanji: false,
		bendijiaohuanji: false,
		yingjianshebei: false,
		moban: false,
		huishouzhan: false,
		xunijibeifen: false,
		xunijikuaizhao: false,
		yunxingrizhi: false,
		qianyirizhi: false,
		xingnengjiankong: false,
		gaojing: false,
		renwu: false,
	}),
});
