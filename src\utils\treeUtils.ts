/**
 * 树形数据处理工具类
 */

export interface TreeNode {
  [key: string]: any;
}

export interface TreeConvertOptions {
  /** 节点ID字段名，默认为 'id' */
  idKey?: string;
  /** 父节点ID字段名，默认为 'pid' */
  pidKey?: string;
  /** 子节点数组字段名，默认为 'children' */
  childrenKey?: string;
  /** 根节点的父ID值，默认为 '0' */
  rootValue?: string | number | null;
}

/**
 * 将扁平的树形数据转换为嵌套的树形结构
 * @param flatData 扁平数据数组
 * @param options 配置选项
 * @returns 嵌套的树形结构数组
 * 
 * @example
 * ```typescript
 * const flatData = [
 *   { id: '1', name: '根节点', pid: '0' },
 *   { id: '2', name: '子节点1', pid: '1' },
 *   { id: '3', name: '子节点2', pid: '1' },
 *   { id: '4', name: '孙节点1', pid: '2' }
 * ];
 * 
 * const treeData = convertFlatToTree(flatData);
 * // 结果：
 * // [
 * //   {
 * //     id: '1', name: '根节点', pid: '0',
 * //     children: [
 * //       {
 * //         id: '2', name: '子节点1', pid: '1',
 * //         children: [
 * //           { id: '4', name: '孙节点1', pid: '2', children: [] }
 * //         ]
 * //       },
 * //       { id: '3', name: '子节点2', pid: '1', children: [] }
 * //     ]
 * //   }
 * // ]
 * ```
 */
export const convertFlatToTree = (
  flatData: TreeNode[],
  options: TreeConvertOptions = {}
): TreeNode[] => {
  const {
    idKey = 'id',
    pidKey = 'pid',
    childrenKey = 'children',
    rootValue = '0'
  } = options;

  if (!Array.isArray(flatData) || flatData.length === 0) {
    return [];
  }

  // 创建一个映射表，用于快速查找节点
  const nodeMap = new Map<string | number, TreeNode>();
  const result: TreeNode[] = [];

  // 第一遍遍历：创建所有节点的映射，并初始化children数组
  flatData.forEach(item => {
    const nodeId = item[idKey];
    if (nodeId !== undefined && nodeId !== null) {
      nodeMap.set(nodeId, { ...item, [childrenKey]: [] });
    }
  });

  // 第二遍遍历：建立父子关系
  flatData.forEach(item => {
    const nodeId = item[idKey];
    const parentId = item[pidKey];
    const node = nodeMap.get(nodeId);

    if (!node) return;

    // 判断是否为根节点
    if (parentId === rootValue || parentId === null || parentId === undefined) {
      result.push(node);
    } else {
      // 查找父节点并添加到其children中
      const parent = nodeMap.get(parentId);
      if (parent) {
        parent[childrenKey].push(node);
      } else {
        // 如果找不到父节点，作为根节点处理（容错处理）
        console.warn(`找不到父节点 ${parentId}，节点 ${nodeId} 将作为根节点处理`);
        result.push(node);
      }
    }
  });

  return result;
};

/**
 * 将嵌套的树形结构转换为扁平数组
 * @param treeData 树形数据数组
 * @param options 配置选项
 * @returns 扁平数据数组
 * 
 * @example
 * ```typescript
 * const treeData = [
 *   {
 *     id: '1', name: '根节点',
 *     children: [
 *       { id: '2', name: '子节点1', children: [] },
 *       { id: '3', name: '子节点2', children: [] }
 *     ]
 *   }
 * ];
 * 
 * const flatData = convertTreeToFlat(treeData, { pidKey: 'pid', rootValue: '0' });
 * // 结果：
 * // [
 * //   { id: '1', name: '根节点', pid: '0' },
 * //   { id: '2', name: '子节点1', pid: '1' },
 * //   { id: '3', name: '子节点2', pid: '1' }
 * // ]
 * ```
 */
export const convertTreeToFlat = (
  treeData: TreeNode[],
  options: TreeConvertOptions = {}
): TreeNode[] => {
  const {
    idKey = 'id',
    pidKey = 'pid',
    childrenKey = 'children',
    rootValue = '0'
  } = options;

  if (!Array.isArray(treeData) || treeData.length === 0) {
    return [];
  }

  const result: TreeNode[] = [];

  const traverse = (nodes: TreeNode[], parentId: string | number | null = rootValue) => {
    nodes.forEach(node => {
      const { [childrenKey]: children, ...nodeWithoutChildren } = node;
      
      // 添加当前节点到结果数组
      result.push({
        ...nodeWithoutChildren,
        [pidKey]: parentId
      });

      // 递归处理子节点
      if (Array.isArray(children) && children.length > 0) {
        traverse(children, node[idKey]);
      }
    });
  };

  traverse(treeData);
  return result;
};

/**
 * 在树形数据中查找指定节点
 * @param treeData 树形数据数组
 * @param predicate 查找条件函数
 * @param childrenKey 子节点字段名
 * @returns 找到的节点或null
 */
export const findNodeInTree = (
  treeData: TreeNode[],
  predicate: (node: TreeNode) => boolean,
  childrenKey: string = 'children'
): TreeNode | null => {
  for (const node of treeData) {
    if (predicate(node)) {
      return node;
    }
    
    if (Array.isArray(node[childrenKey]) && node[childrenKey].length > 0) {
      const found = findNodeInTree(node[childrenKey], predicate, childrenKey);
      if (found) {
        return found;
      }
    }
  }
  
  return null;
};

/**
 * 获取节点在树中的路径
 * @param treeData 树形数据数组
 * @param targetId 目标节点ID
 * @param idKey ID字段名
 * @param childrenKey 子节点字段名
 * @returns 从根节点到目标节点的路径数组
 */
export const getNodePath = (
  treeData: TreeNode[],
  targetId: string | number,
  idKey: string = 'id',
  childrenKey: string = 'children'
): TreeNode[] => {
  const path: TreeNode[] = [];

  const findPath = (nodes: TreeNode[]): boolean => {
    for (const node of nodes) {
      path.push(node);
      
      if (node[idKey] === targetId) {
        return true;
      }
      
      if (Array.isArray(node[childrenKey]) && node[childrenKey].length > 0) {
        if (findPath(node[childrenKey])) {
          return true;
        }
      }
      
      path.pop();
    }
    
    return false;
  };

  findPath(treeData);
  return path;
};

/**
 * 过滤树形数据
 * @param treeData 树形数据数组
 * @param predicate 过滤条件函数
 * @param childrenKey 子节点字段名
 * @returns 过滤后的树形数据
 */
export const filterTree = (
  treeData: TreeNode[],
  predicate: (node: TreeNode) => boolean,
  childrenKey: string = 'children'
): TreeNode => {
  return treeData.reduce((result: TreeNode[], node) => {
    const filteredChildren = Array.isArray(node[childrenKey]) 
      ? filterTree(node[childrenKey], predicate, childrenKey)
      : [];
    
    if (predicate(node) || filteredChildren.length > 0) {
      result.push({
        ...node,
        [childrenKey]: filteredChildren
      });
    }
    
    return result;
  }, []);
};
