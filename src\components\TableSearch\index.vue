<template>
	<div class="table-search-container" v-if="props.search.length > 0">
		<el-form ref="tableSearchRef" inline :model="state.form" class="table-form">
			<template v-for="(val) in search">
				<template v-if="state.isToggle || val.isShow">
					<template v-if="val.type !== ''">
						<el-form-item :key="val.prop" :label="val.label" :prop="val.prop"
							:rules="[{ required: val.required, message: `${val.label}不能为空`, trigger: val.type === 'input' ? 'blur' : 'change' }]">
							<el-input v-model="state.form[val.prop]" :placeholder="val.placeholder"
								:clearable="val.clearable" v-if="val.type === 'input'" style="width: 100%" />
							<el-date-picker v-model="state.form[val.prop]" type="date" :placeholder="val.placeholder"
								v-else-if="val.type === 'date'" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
								style="width: 100%" />
							<el-date-picker v-model="state.form[val.prop]" type="datetime"
								:placeholder="val.placeholder" v-else-if="val.type === 'datetime'"
								format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
							<el-date-picker v-model="state.form[val.prop]" type="daterange" range-separator="-"
								start-placeholder="开始时间" end-placeholder="结束时间" :placeholder="val.placeholder"
								v-else-if="val.type === 'daterange'" value-format="YYYY-MM-DD HH:mm:ss"
								format="YYYY-MM-DD HH:mm:ss" />
							<el-date-picker v-model="state.form[val.prop]" type="datetimerange" range-separator="-"
								start-placeholder="开始时间" end-placeholder="结束时间" :placeholder="val.placeholder"
								v-else-if="val.type === 'datetimerange'" value-format="YYYY-MM-DD HH:mm:ss"
								format="YYYY-MM-DD HH:mm:ss" />
							<el-select v-model="state.form[val.prop]" :multiple="val.isMultiple"
								:clearable="val.clearable" @change="onchange(val.prop, $event)"
								:placeholder="val.placeholder" v-else-if="val.type === 'select'" style="width: 100%">
								<el-option v-for="item in val.options" :key="item.value" :label="item.label"
									:value="item.value"> </el-option>
							</el-select>
						</el-form-item>
					</template>
				</template>
			</template>
			<el-form-item class="table-form-btn" :label-width="search.length <= 1 ? '10px' : '100px'">
				<template #label
					v-if="search.length > 1 && search.filter(item => { return item.isShow === false }).length > 0">
					<div class="table-form-btn-toggle ml10" @click="state.isToggle = !state.isToggle">
						<span>{{ state.isToggle ? '收起' : '展开' }}</span>
						<SvgIcon :name="state.isToggle ? 'ele-ArrowUp' : 'ele-ArrowDown'" />
					</div>
				</template>
				<div>
					<el-button type="warning" v-antiShake="onSearch"
						@click="onSearch">查询</el-button>
					<el-button type="info" class="ml10" @click="onReset">重置</el-button>
				</div>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts" name="TableSearch">
import { reactive, ref, onMounted } from 'vue';
import type { FormInstance } from 'element-plus';
import SvgIcon from '../svgIcon/index.vue';

// 定义空对象类型
interface EmptyObjectType {
  [key: string]: any;
}

// 定义选项类型
interface SelectOptionType {
  value: string | number;
  label: string;
}

// 定义搜索类型
interface TableSearchType {
  label: string;
  prop: string;
  placeholder: string;
  required: boolean;
  type: string;
  options?: SelectOptionType[];
  isShow: boolean;
  isMultiple?: boolean;
  clearable?: boolean;
}

// 定义父组件传过来的值
const props = defineProps({
	// 搜索表单
	search: {
		type: Array<TableSearchType>,
		default: () => [],
	},
	searchParams: {
		type: Object,
		default: () => ({}),
	},
});

// 定义子组件向父组件传值/事件
const emit = defineEmits(['search', 'onchange']);

// 定义变量内容
const tableSearchRef = ref<FormInstance>();
const state = reactive({
	form: {} as EmptyObjectType,
	isToggle: false,
});

// 初始化 form 字段，取自父组件 search.prop
const initFormField = () => {
	if (props.search.length <= 0) return false;
	props.search.forEach((v) => (state.form[v.prop] = ''));
	Object.assign(state.form, props.searchParams);
};

// 查询
const onSearch = () => {
	tableSearchRef.value?.validate((valid: boolean) => {
		if (valid) {
			emit('search', state.form);
		}
	});
};

// 选择变更
const onchange = (prop: string, value: string) => {
	emit('onchange', {
		prop,
		value
	});
};

// 重置
const onReset = () => {
	tableSearchRef.value?.resetFields();
	initFormField();
};

// 暴露方法给父组件
defineExpose({
	onSearch,
	onReset,
	form: state.form
});

// 页面加载时
onMounted(() => {
	initFormField();
});
</script>

<style scoped lang="scss">
.table-search-container {
	display: flex;
  flex: 1;
	.table-form {
		flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    
    :deep(.el-form-item) {
      margin-right: 10px;
      margin-bottom: 10px;
      min-width: 200px;
    }
    
		.table-form-btn-toggle {
			white-space: nowrap;
			user-select: none;
			display: flex;
			align-items: center;
			color: var(--el-color-primary);
		}
    
		:deep(.el-range-input) {
			width: 100%;
		}
    
    .table-form-btn {
      margin-left: auto;
      min-width: auto;
    }
    
    .ml10 {
      margin-left: 10px;
    }
	}
}
</style> 