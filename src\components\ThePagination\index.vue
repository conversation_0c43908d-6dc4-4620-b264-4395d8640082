<template>
  <div class="the-pagination">
    <el-pagination
      v-model:current-page="currentPageSync"
      v-model:page-size="pageSizeSync"
      :background="background"
      :page-sizes="pageSizes"
      :layout="layout"
      :total="total"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      :small="small"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 分页相关
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  total: {
    type: Number,
    default: 0
  },
  
  // 样式相关
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  small: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  hideOnSinglePage: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:currentPage',
  'update:pageSize',
  'change'  // 统一事件
])

// 双向绑定
const currentPageSync = computed({
  get: () => props.currentPage,
  set: (val) => emit('update:currentPage', val)
})

const pageSizeSync = computed({
  get: () => props.pageSize,
  set: (val) => emit('update:pageSize', val)
})

// 事件处理
const handleSizeChange = (size) => {
  emit('change', { page: currentPageSync.value, size })
}

const handleCurrentChange = (page) => {
  emit('change', { page, size: pageSizeSync.value })
}
</script>

<style scoped>
.the-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 