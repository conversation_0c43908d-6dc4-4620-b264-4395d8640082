<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<el-menu :default-active="state.active" class="el-menu-vertical-demo" @select="onClose">
							<el-menu-item index="IPsan" v-if="powerItem.IPsan">
								<template #title>
									<span>IPsan</span>
								</template>
							</el-menu-item>
							<el-menu-item index="FCsan" v-if="powerItem.FCsan">
								<template #title>
									<span>FCsan</span>
								</template>
							</el-menu-item>
							<el-menu-item index="NAS" v-if="powerItem.NAS">
								<template #title>
									<span>NAS</span>
								</template>
							</el-menu-item>
							<el-menu-item index="NVME" v-if="powerItem.NVME">
								<template #title>
									<span>NVME</span>
								</template>
							</el-menu-item>
							<el-menu-item index="distributed" v-if="powerItem.fenbushicunchu">
								<template #title>
									<span>分布式存储</span>
								</template>
							</el-menu-item>
						</el-menu>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<!-- 接入 -->
					<IPsan v-if="state.active == 'IPsan'"></IPsan>
					<FCsan v-if="state.active == 'FCsan'"></FCsan>
					<NAS v-if="state.active == 'NAS'"></NAS>
					<NVME v-if="state.active == 'NVME'"></NVME>
					<Distributed v-if="state.active == 'distributed'"></Distributed>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { powerCodeQuery } from '/@/api/System'; // 权限
// 引入组件 接入
const IPsan = defineAsyncComponent(() => import('./ipsan/index.vue'));
const FCsan = defineAsyncComponent(() => import('./fcsan/index.vue'));
const NAS = defineAsyncComponent(() => import('./nas/index.vue'));
const NVME = defineAsyncComponent(() => import('./nvme/index.vue'));
const Distributed = defineAsyncComponent(() => import('./distributed/index.vue'));
// 定义变量内容
const state = reactive({
	active: 'IPsan',
});
const onClose = (item: string) => {
	state.active = item;
}
// 定义变量内容
const powerItem = reactive({
	IPsan: false,
	FCsan: false,
	NAS: false,
	NVME: false,
	fenbushicunchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:['IPsan','FCsan','NAS','NVME','fenbushicunchu']}).then((res:any)=>{
		powerItem.IPsan = res.data.IPsan;
		powerItem.FCsan = res.data.FCsan;
		powerItem.NAS = res.data.NAS;
		powerItem.NVME = res.data.NVME;
		powerItem.fenbushicunchu = res.data.fenbushicunchu;
		if(powerItem.IPsan){
			state.active = 'IPsan';
		}else if(powerItem.FCsan){ 
			state.active = 'FCsan';
		}else if(powerItem.NAS){ 
			state.active = 'NAS';
		}else if(powerItem.NVME){
			state.active = 'NVME';
		}else if(powerItem.fenbushicunchu){
			state.active = 'fenbushicunchu';
		}else {
			state.active = 'null';
		}
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 220px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.el-menu-vertical-demo {
				width: 100%;
				box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
				border-right: 0px;

				.el-menu-item.is-active {
					color: var(--next-color-white) !important;
				}
				.el-menu-item {
					margin-bottom: 20px;
				}
				.el-menu-hover-bg-color, .el-menu-item:hover, .el-menu-item.is-active, .el-sub-menu.is-active .el-sub-menu__title, .el-sub-menu:not(.is-opened):hover .el-sub-menu__title {
					// background: var(--el-menu-active-color)!important;
					color: var(--next-bg-menuBarActiveColor)!important;
				}
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 240px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}

</style>
