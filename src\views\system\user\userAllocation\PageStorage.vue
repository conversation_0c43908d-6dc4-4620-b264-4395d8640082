<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="存储池" prop="tableSelect">
			<el-input v-model="formItem.tableSelect" placeholder="请输入分布式交换机名称" v-show="false" />
			<span>已选 （ {{ formItem.tableSelect.length }} ）</span>
		</el-form-item>
		<el-form-item></el-form-item>
	</el-form>
	<div class="tabs-table-area">
		<my-table ref="tableRef" :pagination="formItem.pagination" :columns="formItem.columns" :request="getTableData" @selectionChange="selectChange">
			<!-- 总容量 -->
			<template #capacity="{ row }">
				<span>{{ capacityConversion(row.capacity) }}</span>
			</template>
			<!-- 已分配容量 -->
			<template #allocation="{ row }">
				<span>{{ capacityConversion(row.allocation) }}</span>
			</template>
			<!-- 可用容量 -->
			<template #available="{ row }">
				<span>{{ capacityConversion(row.available) }}</span>
			</template>
		</my-table>
	</div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { storagePollQuery } from '/@/api/ResourcePool/storage'; // 接口
import { capacityConversion } from '/@/model/resource'; // 表格 正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
	zeroData: {
		type: Object,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['oneOK']);
const formItem = reactive<{
	show: boolean;
	tableSearch: string;
	columns: MyTableColumns[];
	pagination: any;
	tableSelect: any[];
	tableData: any[];
}>({
	show: true,
	tableSearch: '',
	columns: [
		{ type: 'selection', wrap: true },
		{ label: '名称', prop: 'name', sortable: true, align: 'left' },
		{ label: '类型', prop: 'type_code_display', align: 'center' },
		{ label: '总容量', tdSlot: 'capacity', align: 'center' },
		{ label: '可用容量', tdSlot: 'available', align: 'center' },
	], // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	tableSelect: [],
	tableData: [],
});
// 表格选中变化
const selectChange = (row: any) => {
	formItem.tableSelect = row;
};
const propCard = (rule: any, value: any, callback: any) => {
	if (!value || value.length === 0) {
		callback(new Error('请勾选下面存储池'));
	} else {
		callback();
	}
};
const rulesForm = reactive<FormRules>({
	tableSelect: [{ validator: propCard, trigger: 'change' }],
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async (resolve) => {
		await storagePollQuery({
			// _id: formItem.treeID,
			// type: 'host',
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页显示条数
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: formItem.tableSearch, // 收索条件
		})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
watch(
	() => props.zeroData.card,
	(val) => {
		console.log('val', val);
		// formItem.tableData = props.zeroData.card
		refresh();
	}
);
watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			// formRef.value.validate((val) => {
				console.log('val', val);
				// if (val) {
					emit('oneOK', formItem);
				// }
			// });
		}
	}
);
</script>
<style lang="scss" scoped>
.net-config-area {
	width: 100%;
	height: 300px;
	position: relative;
}
.tabs-table-area {
	width: calc(100%);
	height: calc(100% - 50px);
	position: relative;
}
</style>
