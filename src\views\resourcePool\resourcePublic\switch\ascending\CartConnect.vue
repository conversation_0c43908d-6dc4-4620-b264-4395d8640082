<template>
  <el-dialog
    v-model="formItem.isShow"
    title="绑定网卡"
    append-to-body
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
        
      <el-form-item label="物理网卡" prop="cardName">
        <el-select v-model="formItem.cardName" style="width: 100%" @change="cardChange">
          <el-option v-for="item in formItem.cardData" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { nicQuery,nicBind,nicUnbind } from '/@/api/Network'; // 接口

const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  switchName: '',
  switchID: '',
  cardName: '',
  hostID: '',
  cardData: [{name: '', id: '',host_id: ''}],
});

const rules = reactive<FormRules>({
  cardName: [
    { required: true, message: '必填项', trigger: 'change' },
  ],
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        nicBind({
          switch_id: formItem.switchID,
          nic_name: formItem.cardName,
          host_id: formItem.hostID 
        })
        .then((res:any) => {
          emit('returnOK', 'refresh');
        })
      }
    })
  }
}
// 获取网卡信息
const getNetCardInfo =()=> { 
  nicQuery({})
		.then(res => {
      formItem.cardData = []
      const result:  any[] = [];
      res.data?.forEach((cluster:any) => {
        cluster.hosts?.forEach((host:any) => {
          host.nics?.forEach((nic:any) => {
            if(nic.is_bridge) {
            }else {
              // 添加网卡信息
              result.push({
                id: nic.name+':'+host.host_id,
                name: host.host_ip+' - '+nic.name,
              });
            }
          });
        });
      });
			formItem.cardData = result
		})
		.catch((error) => {});
}
// 选择
const cardChange = (val: any) => {
  // const selectedItem = formItem.cardData.find((item:any) => item.id === val)
  formItem.cardName = val.split(':')[0]
  formItem.hostID = val.split(':')[1]
}
const openCart = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
    getNetCardInfo()
    formItem.switchName = row.name
    formItem.switchID = row.id
	});
};
// 暴露变量
defineExpose({
	openCart,
});
</script>