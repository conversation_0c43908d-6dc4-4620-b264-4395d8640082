<template>
  <ul class="timeline-list">
    <li v-for="(activity, index) in activities" :key="index" class="timeline-item">
      <div class="timeline-item-icon">
        <i class="el-icon-time"></i>
      </div>
      <div class="timeline-item-content">
        <div class="timeline-item-content-title">
          <span class="timeline-item-content-title-title">{{ activity.content }}</span>
          <span class="timeline-item-content-title-time">{{ activity.timestamp }}</span>
        </div>
      </div>
    </li>
  </ul>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  activities: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>

<style scoped lang="scss">
.timeline-list{
  display: flex;
  flex-direction: column;
  gap: 20px;
  .timeline-item{
    display: flex;
    align-items: center;
    border-radius: 10px;
    background-color: rgba(255,255,255,1);
    box-shadow: 0px 0px 6px 0px rgba(220,222,227,1);
    padding: 20px 30px 20px 20px;
    .timeline-item-icon{
      width: 20px;
      height: 20px;
      background-color: rgba(50,103,255,0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      i{
        border-radius: 50%;
        width: 8px;
        height: 8px;
        background-color: rgba(50,103,255,1);
      }
    }
    .timeline-item-content{
      flex: 1;
      overflow: hidden;
      .timeline-item-content-title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .timeline-item-content-title-title{
          color: rgba(51,51,51,1);
          font-size: 14px;
          text-align: left;
          font-family: SourceHanSansSC-regular;
        }
        .timeline-item-content-title-time{
          height: 30px;
          border-radius: 15px;
          background-color: rgba(235,243,255,1);
          color: rgba(50,103,255,1);
          font-size: 14px;
          text-align: center;
          font-family: SourceHanSansSC-regular;
          border: 1px solid rgba(230,239,252,1);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 20px;
        }
      }
    }
  }
}
</style> 