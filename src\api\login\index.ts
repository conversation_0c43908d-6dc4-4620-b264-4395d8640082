import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * 登录api接口集合
 * @method login 用户登录
 * @method logout 用户退出登录
 */
// export function useLoginApi() {
// 	return {
// 		signIn: (data: object) => {
// 			return request({
// 				url: '/login/login',
// 				method: 'post',
// 				data,
// 			});
// 		},
// 		signOut: (data: object) => {
// 			return request({
// 				url: '/login/logout',
// 				method: 'post',
// 				data,
// 			});
// 		},
// 	};
// }
// 登录
export const logon = (data: object) => {
	return request({
		url: '/login/login',
		method: 'post',
		data,
	});
};
// 登录 退出
export const signOut = () => {
	return request({
		url: '/login/logout',
		method: 'get',
	});
};
// 密码 修改
export const passwordChanges = (data: object) => {
	return request({
		url: '/login/v2/setpassword',
		method: 'put',
		data,
	});
};
// 密码规则 查询
export const codeRuleQuery = () => {
	return request({
		url: '/login/v2/getconfig',
		method: 'get',
	});
};
// 密码规则 提交
export const codeRuleSubmit = (data: object) => {
	return request({
		url: '/login/v2/setconfig',
		method: 'post',
		data,
	});
};
