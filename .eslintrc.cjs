module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 12,
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
  },
  extends: [
    'plugin:vue/vue3-essential',
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  plugins: ['vue', '@typescript-eslint'],
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      rules: {
        'no-undef': 'off',
      },
    },
    {
      files: ['*.vue'],
      rules: {
        'no-undef': 'error',
      },
    },
  ],
  rules: {
    // http://eslint.cn/docs/rules/
    // https://eslint.vuejs.org/rules/
    // https://typescript-eslint.io/rules/no-unused-vars/
    '@typescript-eslint/ban-ts-ignore': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/no-use-before-define': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-redeclare': 'error',
    '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
    '@typescript-eslint/no-unused-vars': ['error'],

    // Vue 规则 - 启用重要的
    'vue/custom-event-name-casing': 'warn',
    'vue/attributes-order': 'off',
    'vue/one-component-per-file': 'error',
    'vue/html-closing-bracket-newline': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/attribute-hyphenation': 'warn',
    'vue/html-self-closing': 'off',
    'vue/no-multiple-template-root': 'off',
    'vue/require-default-prop': 'warn',
    'vue/no-v-model-argument': 'off',
    'vue/no-arrow-functions-in-watch': 'error',
    'vue/no-template-key': 'error',
    'vue/no-v-html': 'warn',
    'vue/comment-directive': 'off',
    'vue/no-parsing-error': 'off',
    'vue/no-deprecated-v-on-native-modifier': 'off',
    'vue/multi-word-component-names': 'off',
    // JavaScript 基础规则
    'no-useless-escape': 'warn',
    'no-sparse-arrays': 'warn',
    'no-prototype-builtins': 'warn',
    'no-constant-condition': 'warn',
    'no-use-before-define': 'error',
    'no-restricted-globals': 'off',
    'no-restricted-syntax': 'off',
    'generator-star-spacing': 'off',
    'no-unreachable': 'error',
    'no-multiple-template-root': 'off',
    'no-unused-vars': 'off', // 使用 @typescript-eslint/no-unused-vars 代替
    'no-v-model-argument': 'off',
    'no-case-declarations': 'warn',
    'no-console': 'warn',
    'no-redeclare': 'off', // 使用 @typescript-eslint/no-redeclare 代替
  },
}; 