<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="system-content-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="scanClick">扫描</el-button>
          <el-button type="primary" plain @click="newClick">添加节点</el-button>
          <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 内存 -->
						<template #mem="{ row }">
              <span>{{row.mem}} GB</span>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-button type="danger" plain @click="deleteClick([row])">删除</el-button>
						</template>
          </my-table>
        </div>
      </div>
		</el-card>
    <TableNew :newTime="state.newTime" @returnOK="returnOK"></TableNew>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
import { managementNodeColumns } from '/@/model/system'; // 表列、正则
import { managementNodeQuery,managementNodeScan,managementNodeDelete } from '/@/api/System'; // 接口
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'));
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const searchRef = ref();
// 定义变量内容
const state = reactive({
  columns: managementNodeColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
  tableSelect: [],
  selectRow: [],
  newTime: '',
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(true){
    let libs = [
      { name: '节点1',ip: '***********',cpu: Math.round(Math.random() * 3),mem: 1,remark: '备注1',id:  'lists1'},
      { name: '节点2',ip: '***********',cpu: Math.round(Math.random() * 3),mem: 2,remark: '备注2',id:  'lists2'},
    ]
		return {
			data: libs, // 数据
			total: libs.length, // 总数
		};
  }
  return new Promise(async(resolve)=>{
    managementNodeQuery({

    }).then((res:any)=>{
      resolve({
        data: res, // 数据
        total: res.length // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 添加节点
const newClick = ()=>{
  state.newTime = ''+new Date()
}
// 扫描
const scanClick= ()=>{
  managementNodeScan()
    .then(res => {
      refresh()
    })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 删除节点
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }
  else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '节点/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    managementNodeDelete({
      usernames: formDelet.tableNames,
      userids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除节点操作完成');
      }else {
        ElMessage.error('删除节点操作失败');
      }
    })
  }else {
    refresh()
  }
}

onMounted(() => {})
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .system-content-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
