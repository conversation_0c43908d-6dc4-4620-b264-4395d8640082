<template>
  <div class="resource-pool-container">
    <div class="colony-information-area">
      <p class="information-title"><span @click="summaryQuery">虚拟机信息</span></p>
      <div class="colony-content">
        <img src="../../../assets/resource/wlj.jpg" alt="">
        <div><span>虚拟机名称:</span><span></span><span>{{ state.framework }}</span></div>
        <div><span>虚拟机IP地址:</span><span></span><span>{{ state.physical }} 台</span></div>
        <div><span>物理机IP地址:</span><span></span><span>{{ state.vm }} 台</span></div>
        <div><span>系统类型:</span><span></span><span>{{ state.cpuCore }}</span></div>
        <div><span>系统版本:</span><span></span><span>{{ state.cpuNucleus }}</span></div>
        <div><span>状态:</span><span></span><span>{{ state.cpuRun }}</span></div>
        <div><span>HostName:</span><span></span><span>{{ state.cpuUsed }}</span></div>
        <div><span>UUID:</span><span></span><span>{{ state.cpuUsed }}</span></div>
        <div><span>VNC端口号:</span><span></span><span>{{ state.cpuUsed }}</span></div>
        <div><span>SPICE端口号:</span><span></span><span>{{ state.cpuUsed }}</span></div>
      </div>
    </div>
    <div class="middle-information-area">
      <div class="config-information-area">
        <p class="information-title"><span>配置信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cpu.jpg" alt="">
              <div>
                <p><span>CPU总频率:</span><span>{{ state.cpuFrequencyTotal }}</span></p>
                <p><span>已使用频率:</span><span>{{ state.cpuFrequencyUsed }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>CPU使用率</span>
              <el-progress :percentage="state.cpuRate"/>
            </div>
          </div>
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/nc.jpg" alt="">
              <div>
                <p><span>内存:</span><span>{{ state.mem }}</span></p>
                <p><span>已使用内存:</span><span>{{ state.memUsed }}</span></p>
                <p><span>已分配内存:</span><span>{{ state.memAssigned }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>内存使用率</span>
              <el-progress :percentage="state.memRate"/>
            </div>
          </div>
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cc.jpg" alt="">
              <div>
                <p><span>存储:</span><span>{{ state.stor }}</span></p>
                <p><span>已使用存储:</span><span>{{ state.storUsed }}</span></p>
                <p><span>已分配存储:</span><span>{{ state.storAssigned }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>存储使用率</span>
              <el-progress :percentage="state.storRate"/>
            </div>
          </div>
        </div>
      </div>
      <div class="resource-allocation-area">
         <p class="information-title"><span>资源分配</span></p>
      </div>
    </div>
    <div class="right-information-area">
      <div class="strategy-information-area">
        <p class="information-title"><span>策略配置</span></p>
      </div>
      <div class="strategy-information-area">
        <p class="information-title-long"><span>安全信息</span></p>
      </div>
      <div class="resource-usage-area">
        <p class="information-title-long"><span>虚拟机杀毒</span></p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool';
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  framework: '-',
  physical: '-',
  vm: '-',
  cpuCore: '-',
  cpuNucleus: '-',
  cpuRun: '-',
  cpuUsed: '-',
  cpuFrequencyTotal: '-',
  cpuFrequencyUsed: '-',
  cpuRate: 0,
  mem: '-',
  memUsed: '-',
  memAssigned: '-',
  memRate: 0,
  stor: '-',
  storUsed: '-',
  storAssigned: '-',
  storRate: 0,
});
// 概要 数据
const summaryQuery=()=>{
	let libs = ['测试1', '测试2', '测试3', '测试4'];
  state.framework = libs[Math.round(Math.random() * 3)]
  // vmOverview(props.treeItem.id).then(res=>{
  //   state.physical = res.data.host_count
  //   state.vm = res.data.vm_count
  // })
}
onMounted(() => {
  summaryQuery()
})
watch(
  ()=> props.treeItem,
  (val)=>{
    summaryQuery()
  }
);
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    display: flex;
    justify-content: space-between;
    .colony-information-area {
      width: 600px;
		  height: calc(100%);
      border: 1px solid var(--el-card-border-color);
      border-radius: var(--el-card-border-radius);
      box-shadow: var(--el-box-shadow-light);
      .colony-content {
        img {
          width: 160px;
        }
        width: 100%;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        div {
          span {
            display: inline-block;
            width: 150px;
          }
          span:first-child {
            text-align: right;
          }
          span:nth-child(2) {
            width: 50px;
          }
        }
      }
    }
    .middle-information-area {
      width: 570px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .config-information-area {
        width: 100%;
        height: 450px;
        margin-bottom: 20px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .config-content {
          width: 100%;
          padding: 0 60px;
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
        }
      }
      .resource-allocation-area {
        width: 100%;
        height: 450px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
      }
    }
    .right-information-area {
      width: 420px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .strategy-information-area {
        width: 100%;
        height: 300px;
        margin-bottom: 20px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
      }
      .resource-usage-area {
        width: 100;
        height: 700px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-title-long {
          height: 50px;
          display: flex;
          align-items: flex-end;
          span {
            display: inline-block;
            text-align: center;
            width: 180px;
            line-height: 30px;
            background-image: url('/@/assets/resource/title.jpg');
            background-size: 100% 100%;
          }
        }
      }
    }
    
  }
  .information-title {
    height: 50px;
    display: flex;
    align-items: flex-end;
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 130px;
    .general-img {
      display: flex;
      img {
        width: 120px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 350px;
      }
    }
  }
</style>