<template>
	<div class="login-container flex">
		<div class="login-content flex">
			<div class="login-logo"></div>
			<div class="login-content-warp-mian">
				<div class="login-content-warp-main-form">
					<div class="login-content-info">{{ $t('message.account.accountTitle') }}</div>
					<div class="login-content-title">{{ $t('message.account.title') }}</div>
					<Account />
				</div>
			</div>
			<!-- <div class="login-bottom">
				<div>
					<span>欢迎使用</span>
					<span>应用版本号{{ themeConfig.info.version_information }}</span>
					<span>建议使用Chrome87及以上浏览器</span>
				</div>
				<div>
					<span>版权所有：</span>
					<span>{{ themeConfig.info.copyright_information }}</span>
					<span>技术支持：</span>
					<span>{{ themeConfig.info.copyright_information }}</span>
				</div>
			</div> -->
		</div>
	</div>
</template>

<script setup lang="ts">
	import { defineAsyncComponent, onMounted, reactive, computed } from 'vue';
	import { storeToRefs } from 'pinia';
	import { useThemeConfig } from '/@/stores/themeConfig';
	import { NextLoading } from '/@/utils/loading';

	// 引入组件
	const Account = defineAsyncComponent(() => import('/@/views/login/component/Account.vue'));

	// 定义变量内容
	const storesThemeConfig = useThemeConfig();
	const { themeConfig } = storeToRefs(storesThemeConfig);
	const state = reactive({
		tabsActiveName: 'account',
		isScan: false,
	});

	// 获取布局配置信息
	const getThemeConfig = computed(() => {
		return themeConfig.value;
	});
	// 页面加载时
	onMounted(() => {
		NextLoading.done();
	});
</script>

<style scoped lang="scss">
	.login-container {
		height: 100%;
		min-width: 1200px;
		overflow: auto;
		font-family: 'MicrosoftYaHei';
		// background-color: rgba(203, 203, 203, 0.18);
		background-image: url('/@/assets/login/bg.png');
		background-size: 100%;
		background-repeat: round;
		.login-content {
			width: calc(100%);
			position: relative;
			.login-logo {
				position: absolute;
				left: 50px;
				top: 40px;
				width: 264px;
				height: 54px;
				background: url('/@/assets/login/logo.png');
				background-size: 100%;
			}
			.login-info {
				position: absolute;
				color: #8c9ca3;
				font-size: 24px;
				left: 450px;
				top: 252px;
			}
			.login-title {
				display: flex;
				justify-content: center;
				width: 100%;
				position: absolute;
				pointer-events: none;
				margin-top: 5%;
				h3 {
					font-size: 40px;
					line-height: 55px;
				}
			}
			.login-content-warp-main-title {
				width: 497px;
				height: 55px;
				margin-left: 3px;
				background: url('/@/assets/login/title.png') no-repeat;
				background-size: 100%;
			}
			.login-content-warp-mian {
				width: 490px;
				// height: 600px;
				position: absolute;
				right: 12%;
				top: 50%;
				transform: translateY(-50%);
				border-radius: 24px;
				background-color: rgba(255, 255, 255, 0.2);
				border: #dfdfdf 1px solid;
				box-shadow: 1px 1px 1px 1px rgba(179, 192, 219, 0.2);
				.login-content-warp-main-form {
					flex: 1;
					.login-content-info {
						font-size: 26px;
						// padding-top: 36px;
						// padding-bottom: 23px;
						letter-spacing: 3px;
						animation: logoAnimation 0.3s ease;
						animation-delay: 0.3s;
						color: var(--el-color-primary);
						font-weight: bold;
						text-align: center;
						height: 60px;
						line-height: 60px;
					}
					.login-content-title {
						font-size: 20px;
						// padding-bottom: 40px;
						letter-spacing: 3px;
						animation: logoAnimation 0.3s ease;
						animation-delay: 0.3s;
						color: #1a1a1a;
						font-weight: bold;
						text-align: center;
						height: 50px;
						line-height: 50px;
					}
					.login-content-main-sacn {
						position: absolute;
						top: 0;
						right: 0;
						width: 50px;
						height: 50px;
						overflow: hidden;
						cursor: pointer;
						transition: all ease 0.3s;
						color: var(--el-color-primary);
						&-delta {
							position: absolute;
							width: 35px;
							height: 70px;
							z-index: 2;
							top: 2px;
							right: 21px;
							background: var(--el-color-white);
							transform: rotate(-45deg);
						}
						&:hover {
							opacity: 1;
							transition: all ease 0.3s;
							color: var(--el-color-primary) !important;
						}
						i {
							width: 47px;
							height: 50px;
							display: inline-block;
							font-size: 48px;
							position: absolute;
							right: 1px;
							top: 0px;
						}
					}
				}
			}
			.login-bottom {
				position: absolute;
				text-align: center;
				bottom: 12px;
				transform: translateX(-50%);
				left: 50%;
				color: #8c9ca3;
				>div {
					line-height: 24px;

					>span {
						margin: 0 6px;
					}
				}
			}
		}
	}
</style>
