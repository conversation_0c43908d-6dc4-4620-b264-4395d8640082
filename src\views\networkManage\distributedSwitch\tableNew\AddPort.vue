<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="默认端口组" prop="portName">
			<el-input v-model="formItem.portName" placeholder="请输入分布式交换机名称" />
		</el-form-item>
		<!-- <el-form-item label="端口类型" prop="portType">
			<el-radio-group v-model="formItem.portType">
				<el-radio value="pt">普通</el-radio>
				<el-radio value="zj">中继</el-radio>
			</el-radio-group>
		</el-form-item> -->
		<el-form-item label="默认端口组VLAN" prop="portVlan">
      <el-input v-model="formItem.portVlan" type="number" placeholder="有效值1-4096"/>
		</el-form-item>
		<!-- <a class="new-show" @click="formItem.networkPolicy = !formItem.networkPolicy"
			>
       <span>网络策略</span>
			<el-icon v-if="!formItem.networkPolicy"><ArrowRightBold /></el-icon>
			<el-icon v-if="formItem.networkPolicy"><ArrowDownBold /></el-icon>
		</a>
		<div v-show="formItem.networkPolicy">
			<el-form-item label="外部设备">
				<el-checkbox v-model="formItem.list" label="占位数据" />
			</el-form-item>
		</div> -->
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ArrowRightBold } from '@element-plus/icons-vue'
import { linuxData, windowsData, qitaData } from '/@/model/vm';
import { propName,propVlan } from '/@/model/network'; // 表列、正则
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['portOK']);
const formItem = reactive({
	portName: '',
	portType: 'pt',
	portVlan: '1',
  networkPolicy: false,
	list: false,
});
const rulesForm = reactive<FormRules>({
	portName: [{ validator: propName, trigger: 'change' }],
	portType: [{ required: true, message: '必填项', trigger: 'blur' }],
	portVlan: [
    { required: true, message: '必填项' },
    { validator: propVlan, trigger: "blur" }
  ],
});
watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('portOK', formItem);
				}
			});
		}
	}
);
</script>
<style lang="scss" scoped>
  .new-show {
    display: flex;
    align-items: center;
    font-size: 20px;
    padding: 15px 0;
    cursor: pointer;
    >span {
      margin-right: 10px;
    }
  }
</style>