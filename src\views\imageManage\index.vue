<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="newClick">添加镜像</el-button>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
          </div>
          <div>
            <el-input v-show="false" v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 展开行内容 -->
            <template #expandContent="{ row }">
              <div>
                <p>ID: {{ row.id }}</p>
              </div>
            </template>
            <!-- 扩展 -->
            <template #props="{ row }">
              {{ row }}
						</template>
						<!-- 大小 -->
						<template #size="{ row }">
              {{ formatSize(row.size) }}
						</template>
            <!-- 状态 -->
            <template #status="{ row }">
              <span v-if="row.status == 'active'">可用</span>
              <div v-else>
                {{ convertTask(row.task) }}
                <el-progress :percentage="100" :stroke-width="15" :text-inside="true" :show-text="false" :duration="15" status="warning" striped striped-flow />
              </div>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown trigger="click" @command="commandItem($event,row)">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="bj">修改</el-dropdown-item>
                    <el-dropdown-item command="xtlx">切换系统类型</el-dropdown-item>
                    <el-dropdown-item command="yyp">创建云硬盘</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
    <ImgNEW :newTime="state.newTime" @returnOK="returnOK"></ImgNEW>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search,ArrowDownBold } from '@element-plus/icons-vue'
import { imgColumns,formatSize } from '/@/model/imageNanage'; // 表列、正则
import { imageQuery,imageDelete } from '/@/api/ImageManage'; // 接口

import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const ImgNEW = defineAsyncComponent(() => import('./ImgNEW.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

import { useRouter } from 'vue-router';
import { tr } from 'element-plus/es/locale';
const router = useRouter();
// 定义变量内容
const state = reactive({
  columns: imgColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  tableRow: {},
  newTime: '',
  editTime: '',
  deleteTime: '',

  taskStatus: false,
});

interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(true){
    let libs = ['ISO','WINDOWS','REM','其它']
    const list = new Array(page.pageSize).fill({}).map((item, index) => {
			item = {
        name: '镜像: '+libs[Math.round(Math.random() * 3)],
        os_type: libs[Math.round(Math.random() * 3)],
        size: Math.round(Math.random() * 1824000),
				task: index==0?'list':'active',
        status: index==0?'list':'active',
				id:  'lists'+index,
			};
			return item;
		});

		return {
			data: list, // 数据
			total: 100, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    imageQuery().then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 任务转换
const convertTask = (item: string) => {
  if(item) {
    state.taskStatus = true
  }
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  state.tableRow = row
  switch (item) {
    case 'bj':
      state.editTime = ''+new Date()
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 添加
const newClick = () => {
  state.newTime = ''+new Date()
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '镜像/'+new Date()    
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    imageDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg != 'ok'){
        refresh()
        ElMessage.success('删除镜像操作完成');
      }else {
        ElMessage.error('删除镜像操作失败');
      }
    })
  }else {
    refresh()
  }
}
// watch(()=>state.taskStatus, (newValue, oldValue) => {
//   if(newValue) {
//     nextTick(()=>{
//       ElMessage.warning('任务正在执行中，请勿操作！')
//       // state.taskStatus = false
//     })
//   }
// })
onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
	width: calc(100%);
	height: calc(100%);
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>