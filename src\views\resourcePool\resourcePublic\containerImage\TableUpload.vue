<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="上传磁盘"
    :close-on-click-modal="false"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="目标主机" prop="host">
        <el-input v-model="formItem.host" placeholder="请输入主机IP" />
      </el-form-item>
      <el-form-item label="镜像仓库" prop="repo">
        <el-input v-model="formItem.repo" placeholder="请输入仓库，如：tianwen1:5000/ubuntu" />
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input v-model="formItem.tag" placeholder="请输入标签，如：20.05" />
      </el-form-item>
      <el-form-item label="上传磁盘" prop="file">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :action="uploadAction"
          :limit="1"
          :on-exceed="handleExceed"
          :auto-upload="false"
          :before-upload="beforeUpload"
          :http-request="customUpload"
          :on-change="onChange"
          :on-remove="onRemove"
          :disabled="formItem.disabled"
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
        </el-upload>
        <!-- 上传进度 -->
        <div v-if="formItem.progressAll > 0" class="upload-progress">
          <el-progress
            :percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))"
            :status="formItem.status"
            :stroke-width="8"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelUpload">取消</el-button>
        <el-button type="primary" @click="confirm" :disabled="formItem.disabled || !formItem.file">
          {{ formItem.disabled ? '上传中...' : '开始上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, nextTick, ref } from 'vue';
import { genFileId } from 'element-plus';
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  FormInstance,
  FormRules,
  UploadRequestOptions,
  UploadFile
} from 'element-plus';
import { ElMessage } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { diskSlice, diskUpload } from '/@/api/ResourcePool/storage';
import pLimit from 'p-limit';

const ruleFormRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();

const formItem = reactive({
  isShow: false,
  host: '',
  repo: '',
  tag: '',
  file: null as File | null,
  fileSize: 0,
  progressAll: 0,
  progressUsed: 0,
  status: '' as 'success' | 'exception' | '',
  disabled: false,
  storagePoolId: '', // 存储池ID，需要从外部传入
});

// 表单验证规则
const propFile = (rule: any, value: any, callback: any) => {
  if (!formItem.file) {
    callback(new Error('请选择文件'));
  } else {
    callback();
  }
};

const rules = reactive<FormRules>({
  host: [{ required: true, message: '必选项', trigger: "blur" }],
  repo: [{ required: true, message: '必填项', trigger: "blur" }],
  tag: [{ required: true, message: '必填项', trigger: "blur" }],
  file: [
    { required: true, message: '必选项', trigger: "blur"  },
    { validator: propFile, trigger: 'blur' },
  ],
});

// 上传相关配置
const uploadAction = '/upload/v5/upload/chunk'; // 这个不会被使用，因为我们用自定义上传
const chunkSize = 10 * 1024 * 1024; // 10MB 分片大小

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

// 上传前的钩子
const beforeUpload = (file: File) => {
  formItem.file = file;
  formItem.fileSize = file.size;

  // 触发表单验证
  if (ruleFormRef.value) {
    ruleFormRef.value.validateField('file');
  }

  return false; // 阻止自动上传，我们手动控制
};

// 文件状态改变时的钩子
const onChange = (uploadFile: UploadFile) => {
  if (uploadFile.raw) {
    formItem.file = uploadFile.raw;
    formItem.fileSize = uploadFile.raw.size;
  }
};

// 文件移除时的钩子
const onRemove = () => {
  formItem.file = null;
  formItem.fileSize = 0;
  formItem.progressAll = 0;
  formItem.progressUsed = 0;
  formItem.status = '';
};

// 自定义上传函数（这个不会被调用，因为我们手动控制上传）
const customUpload = (options: UploadRequestOptions) => {
  // 这里不做任何操作，因为我们在 confirm 中手动处理上传
  return Promise.resolve();
};

// 上传单个分片
const uploadChunk = async (chunk: Blob, index: number, total: number, filename: string) => {
  const formData = new FormData();
  formData.append('chunk', chunk);
  formData.append('index', index.toString());
  formData.append('total', total.toString());
  formData.append('filename', filename);
  formData.append('storage_pool_id', formItem.storagePoolId);

  try {
    await diskSlice(formData);
    formItem.progressUsed++;
  } catch (error) {
    formItem.status = 'exception';
    formItem.disabled = false;
    throw error;
  }
};

// 通知上传完成
const notifyComplete = async (total: number, filename: string) => {
  try {
    await diskUpload({
      name: `${formItem.repo}_${formItem.tag}`, // 使用仓库名和标签作为磁盘名
      total: total,
      filename: filename,
      storage_pool_id: formItem.storagePoolId,
      size: formItem.fileSize,
      remark: `容器镜像: ${formItem.repo}:${formItem.tag}`
    });

    formItem.status = 'success';

    setTimeout(() => {
      ElMessage.success('上传磁盘操作完成');
      formItem.isShow = false;
      emit('returnOK', 'refresh');
      resetForm();
    }, 1500);
  } catch (error) {
    formItem.status = 'exception';
    formItem.disabled = false;
    ElMessage.error('上传完成通知失败');
  }
};

// 执行分片上传
const performChunkedUpload = async (file: File) => {
  const chunks = Math.ceil(file.size / chunkSize);
  formItem.progressAll = chunks;
  formItem.progressUsed = 0;
  formItem.status = '';

  const limit = pLimit(3); // 限制并发数为3

  try {
    // 串行上传分片
    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(file.size, start + chunkSize);
      const chunk = file.slice(start, end);

      await limit(() => uploadChunk(chunk, i, chunks, file.name));
    }

    // 通知完成
    await notifyComplete(chunks, file.name);
  } catch (error) {
    ElMessage.error('上传磁盘失败');
    formItem.status = 'exception';
    formItem.disabled = false;
		formItem.progressAll = 0
  }
};

// 重置表单
const resetForm = () => {
  formItem.file = null;
  formItem.fileSize = 0;
  formItem.progressAll = 0;
  formItem.progressUsed = 0;
  formItem.status = '';
  formItem.disabled = false;
  formItem.host = '';
  formItem.repo = '';
  formItem.tag = '';

  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 取消上传
const cancelUpload = () => {
  formItem.isShow = false;
  resetForm();
};

const emit = defineEmits(['returnOK']);

// 确认上传
const confirm = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(val => {
      if (val) {
        if (formItem.file) {
          formItem.disabled = true;
          performChunkedUpload(formItem.file);
        } else {
          ElMessage.warning('请选择要上传的文件');
        }
      }
    });
  }
};

// 打开弹窗
const openDialog = async (treeItem: any, storagePoolId?: string) => {
  formItem.isShow = true;
  formItem.storagePoolId = storagePoolId || '';

  nextTick(() => {
    formItem.host = treeItem.ip;
    resetForm();
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}
.upload-progress {
	margin-top: 10px;
	width: 100%;
}
:deep(.el-upload-dragger) {
  width: 100%;
  height: 90px;
	padding: 0;

  .el-icon--upload {
    font-size: 40px;
    color: #c0c4cc;
    margin-bottom: 0px;
		padding: 0;
  }

  .el-upload__text {
    color: #606266;
    font-size: 14px;

    em {
      color: #409eff;
      font-style: normal;
    }
  }
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}
</style>