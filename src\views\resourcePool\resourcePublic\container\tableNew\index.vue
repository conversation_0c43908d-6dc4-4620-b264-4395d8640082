<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="添加容器"
    class="dialog-800"
  >
    <div class="vm-new-area">
      <div class="vm-step-area">
        <el-steps :active="formItem.current" align-center>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="容器配置" :icon="Discount" />
          <el-step title="数据盘" :icon="ChatLineSquare" />
          <el-step title="环境变量" :icon="CreditCard" />
          <el-step title="标签" :icon="Paperclip" />
        </el-steps>
      </div>
      <div class="vm-new-content">
        <div class="vm-new-common" v-if="formItem.isShow">
          <!-- 基本信息 -->
          <Basic v-show="formItem.current==0" :besicTime="formItem.besicTime" @basicOK="basicOK"></Basic>
          <Spec v-show="formItem.current==1" :specTime="formItem.specTime" @specOK="specOK"></Spec>
          <Volumes v-show="formItem.current==2" :listTime="formItem.listTime" @listOK="listOK"></Volumes>
          <Miscellaneous v-show="formItem.current==3" :listTime="formItem.listTime"  @listOK="listOK"></Miscellaneous>
          <Labels v-show="formItem.current==4" :listTime="formItem.listTime" @listOK="listOK"></Labels>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="formItem.current>0" type="primary" @click="formItem.current--">上一步</el-button>
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button v-if="formItem.current<4" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="formItem.current==4" type="primary" @click="confirm" :loading="formItem.loading">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Tickets, Discount,ChatLineSquare,CreditCard,Paperclip } from '@element-plus/icons-vue'
import { containerNew } from '/@/api/ResourcePool/container'; // 接口
const Basic = defineAsyncComponent(() => import('./Basic.vue'));
const Spec = defineAsyncComponent(() => import('./Spec.vue'));
const Volumes = defineAsyncComponent(() => import('./Volumes.vue'));
const Miscellaneous = defineAsyncComponent(() => import('./Miscellaneous.vue'));
const Labels = defineAsyncComponent(() => import('./Labels.vue'));

interface forminter {
  isShow: boolean,
  loading: boolean,
  current: number,
  besicTime: string,
  formData: any,
  specTime: string,
  listTime: string,
}
const formItem = reactive<forminter>({
  isShow: false,
  loading: false,
  current: 0,
  besicTime: '',
  formData: {},
  specTime: '',
  listTime: '',
});
// 基本信息下一步
const nextStep = () => {
  if(formItem.current == 0) {
    formItem.besicTime = ''+new Date()
  }else if(formItem.current == 1) {
    formItem.specTime = ''+new Date()
  }else {
    formItem.current++
  }
}
// 基本信息数据
const basicOK = (data: any) => {
  formItem.formData = data
  formItem.current++
}
// spec数据
const specOK = (data: any) => {
  formItem.formData = Object.assign({},formItem.formData, data)
  formItem.current++
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.listTime = ''+new Date()
  console.log('容器表单',formItem.formData);
  formItem.loading = true;
  containerNew(formItem.formData)
  .then(res => {
    formItem.isShow = false;
    emit('returnOK', 'refresh');
  })
  .catch(error => {
    formItem.loading = false;
  })
}
const listOK = (data: any) => {
  formItem.formData = Object.assign(formItem.formData, data)
}
// 打开弹窗
const openDialog = async () => {
	nextTick(() => {
		formItem.isShow = true;
    formItem.loading = false;
    formItem.current = 0
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  .vm-new-area {
    height: 480px;
    .vm-step-area {
      margin-bottom: 10px;
      padding: 0 15%;
      height: 70px;
    }
    .vm-new-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;

      .vm-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
        
        .vm-new-leve {
          display: block;
          font-size: 20px;
          margin-bottom: 20px;
          cursor: pointer;
        }
      }
    }
  }
</style>