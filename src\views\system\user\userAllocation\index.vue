<template>
	<el-dialog class="dialog-700" v-model="state.isShow" append-to-body :title="state.title">
		<div class="paging-dialog-area">
			<div class="paging-step-area">
				<el-steps :active="state.current" align-center>
					<el-step title="资源池" :icon="MessageBox" />
					<el-step title="存储池" :icon="TakeawayBox " />
					<el-step title="配额" :icon="Finished" />
				</el-steps>
				<!-- <el-steps :active="state.current" simple>
          <el-step title="资源池" :icon="MessageBox" />
					<el-step title="存储池" :icon="TakeawayBox " />
					<el-step title="配额" :icon="Finished" />
        </el-steps> -->
			</div>
      <div class="paging-basic-content">
        <!-- 资源池 -->
        <div class="paging-new-common" v-show="state.current == 0">
          <PageResources v-if="state.isShow" :times="state.zeroTime" @zeroOK="zeroOK" />
        </div>
        <!-- 存储池 -->
        <div class="paging-new-common" v-show="state.current == 1">
          <PageStorage v-if="state.isShow" :zeroData="state.zeroData" :times="state.oneTime" @oneOK="oneOK" />
        </div>
        <!-- 配额 -->
        <div class="paging-new-common" v-show="state.current == 2">
          <PageQuota v-if="state.isShow" :zeroData="state.zeroData" :times="state.twoTime" @twoOK="twoOK" />
        </div>
      </div>
		</div>
		<template #footer>
      <div class="dialog-footer">
        <el-button v-if="state.current > 0" type="primary" @click="state.current--">上一步</el-button>
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button v-if="state.current < 2" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="state.current == 2" type="primary" @click="confirm" :loading="state.loading">确认</el-button>
      </div>
    </template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { MessageBox, TakeawayBox , Finished } from '@element-plus/icons-vue'

const PageResources = defineAsyncComponent(() => import('./PageResources.vue'));
const PageStorage = defineAsyncComponent(() => import('./PageStorage.vue'));
const PageQuota = defineAsyncComponent(() => import('./PageQuota.vue'));
import { ElMessage } from 'element-plus';
import { distributedSwitchNew } from '/@/api/Network'; // 接口

// 定义变量内容
const state = reactive({
  isShow: false,
  title: '资源分配',
  loading: false,
  current: 0,
  zeroTime: '',
  zeroData: {
    name: '',
  },
  oneTime: '',
  oneData: {
    type: '',
    tableData: [],
  },
  twoTime: '',
  twoData: {
    portName: '',
    portVlan: '',
  },
});
// 基本信息数据
const zeroOK = (data: any) => {
  state.zeroData = data
  state.current++
}
// 网络配置
const oneOK = (data: any) => {
  state.oneData = data
  state.current++
}
// 默认端口组
const twoOK = (data: any) => {
  state.twoData = data
  state.current++
}
// 下一步
const nextStep = () => {
  if (state.current == 0) {
    state.zeroTime = ""+new Date()
  }else if (state.current == 1) {
    state.oneTime = ""+new Date()
  }else if (state.current == 2) {
    state.twoTime = ""+new Date()
  }
}
const emit = defineEmits(['returnOK']);
// 确认
const confirm =()=>{
  distributedSwitchNew({
    name: state.zeroData.name,
    net_type: state.oneData.type,
    net_data: state.oneData.tableData,
    port_group_name: state.twoData.portName,
    vlan_id: state.twoData.portVlan,
  })
  .then(res => {
    if(res.code == 200) {
      state.isShow = false;
      emit('returnOK', 'refresh');
      ElMessage.success('资源分配操作已完成');
    }else {
      ElMessage.error(res.msg);
    }
  })
  .catch(err => {
    ElMessage.error(err.msg);
  })
  emit('returnOK', 'refresh');
}
const openDialog = (row:any) => {
  state.isShow = true;
  state.loading = false;
  state.current = 0
  nextTick(() => {
    state.title = row.name + '资源分配'
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  .paging-dialog-area {
    height: 600px;
    .paging-step-area {
      padding: 0 15%;
      height: 70px;
    }
    .paging-basic-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;
      .paging-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
      }
    }
  }

</style>