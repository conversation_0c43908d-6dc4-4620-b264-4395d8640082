import request from '/@/utils/request';
// 分布式交换机 查询
export const distributedSwitchQuery = (data: object) => {
	return request({
		url: '/theapi/v5/distributed/switch/list',
		method: 'post',
		data,
	});
};
// 分布式交换机 添加
export const distributedSwitchNew = (data: object) => {
	return request({
		url: '/theapi/v5/distributed/switch/add',
		method: 'post',
		data,
	});
};
// 分布式交换机 添加-网卡
export const nicQuery = (data: object) => {
	return request({
		url: '/theapi/v5/nic/list',
		method: 'post',
		data,
	});
};
// 分布式交换机 修改
export const distributedSwitchEdit = (data: object) => {
	return request({
		url: '/theapi/v5/distributed/switch/edit',
		method: 'post',
		data,
	});
};
// 分布式交换机 删除
export const distributedSwitchDelete = (data: object) => {
	return request({
		url: '/theapi/v5/distributed/switch/delete',
		method: 'delete',
		data,
	});
};


// 本地交换机 查询
export const localSwitchQuery = (data: object) => {
	return request({
		// url: '/theapi/v5/host/switch/list',
		url: '/theapi/v5/switch/list',
		method: 'post',
		data,
	});
};
// 本地交换机 添加
export const localSwitchNew = (data: object) => {
	return request({
		// url: '/theapi/v5/host/switch/add',
		url: '/theapi/v5/switch/add',
		method: 'post',
		data,
	});
};
// 本地交换机 修改-查询适配器
export const adapterQuery = (data: object) => {
	return request({
		url: '/theapi/v5/switch/list',
		method: 'post',
		data,
	});
};
// 本地交换机 修改
export const localSwitchEdit = (data: object) => {
	return request({
		// url: '/theapi/v5/local/switch/update',
		// method: 'post',
		url: '/theapi/v5/switch/update',
		method: 'put',
		data,
	});
};
// 本地交换机 删除
export const localSwitchDelete = (data: object) => {
	return request({
		url: '/theapi/v5/switch/delete',
		method: 'delete',
		data,
	});
};
// 主机全部查询
export const hostAllQuery = () => {
	return request({
		url: '/theapi/v5/host/lists',
		method: 'get',
	});
};
// 集群全部查询
export const colnyAllQuery = () => {
	return request({
		url: '/theapi/v5/cluster/lists',
		method: 'get',
	});
};

// 端口组 查询
export const portGroupQuery = (data: object) => {
	return request({
		// url: '/theapi/v5/switch/port/list',
		url: '/theapi/v5/port/group/list',
		method: 'post',
		data,
	});
};
// 端口组 添加
export const portGroupNew = (data: object) => {
	return request({
		// url: '/theapi/v5/host/switch/port/create',
		url: '/theapi/v5/port/group/add',
		method: 'post',
		data,
	});
};
// 端口组 修改
export const portGroupEdit = (data: object) => {
	return request({
		url: '/theapi/v5/port/group/update',
		method: 'put',
		data,
	});
};
// 端口组 删除
export const portGroupDelete = (data: object) => {
	return request({
		url: '/theapi/v5/port/group/delete',
		method: 'delete',
		data,
	});
};
// 上行 查询
export const ascendingQuery = (data: object) => {
	return request({
		url: '/theapi/v5/switch/nic',
		method: 'post',
		data,
	});
};
// 网卡 绑定
export const nicBind = (data: object) => {
	return request({
		url: '/theapi/v5/switch/bind/nic',
		method: 'post',
		data,
	});
};
// 网卡 解绑
export const nicUnbind = (data: object) => {
	return request({
		url: '/theapi/v5/switch/unbind/nic',
		method: 'post',
		data,
	});
};
// 下行 查询
export const descendingQuery = (data: object) => {
	return request({
		url: '/theapi/v5/switch/vms',
		method: 'post',
		data,
	});
};

// 网络 查询
export const netQuery = () => {
	return request({
		url: '/theapi/v3/networks',
		method: 'get',
	});
};
// 网络 添加
export const netNew = (data: object) => {
	return request({
		url: '/theapi/v1/create/adminnetworks',
		method: 'post',
		data,
	});
};
// 网络 修改
export const netEdit = (data: object) => {
	return request({
		url: '/theapi/v2/networks/edit',
		method: 'post',
		data,
	});
};
// 网络 删除
export const netDelete = (data: object) => {
	return request({
		url: '/theapi/v1/networks/delete',
		method: 'delete',
		data,
	});
};
// 网络 子网新建
export const netSubnetNew = (data: object) => {
	return request({
		url: '/theapi/v1/networks/create/subnet',
		method: 'post',
		data,
	});
};
// 网络 子网编辑
export const netSubnetEdit = (data: object) => {
	return request({
		url: '/theapi/v1/networks/edit/subnet',
		method: 'post',
		data,
	});
};
// 网络 子网删除
export const netSubnetDelete = (data: object) => {
	return request({
		url: '/theapi/v1/networks/delete/subnet',
		method: 'post',
		data,
	});
};
// 安全组 查询
export const secureGroupQuery = () => {
	return request({
		url: '/theapi/v1/securitygroups/list',
		method: 'get',
	});
};
// 安全组 添加
export const secureGroupNew = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/create',
		method: 'post',
		data,
	});
};
// 安全组 修改
export const secureGroupEdit = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/edit',
		method: 'post',
		data,
	});
};
// 安全组 删除
export const secureGroupDelete = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/delete',
		method: 'post',
		data,
	});
};
// 规则表 查询
export const ruleTableQuery = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/detail',
		method: 'post',
		data,
	});
};
// 规则表 添加
export const ruleTableNew = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/rule/create',
		method: 'post',
		data,
	});
};
// 规则表 删除
export const ruleTableDelete = (data: object) => {
	return request({
		url: '/theapi/v1/securitygroups/rule/delete',
		method: 'post',
		data,
	});
};
