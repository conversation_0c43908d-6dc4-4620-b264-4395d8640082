<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="props.actionType"
    class="dialog-500"
  >
    <div>
      <span>是否{{ props.actionType }}下列告警规则？</span>
      <p :style="{color:props.actionType=='启用'?'green':'red',wordWrap: 'break-word'}">{{formItem.names.toString()}}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { alarmRulesAction,alarmRulesDelete } from '/@/api/LogManage'; // 接口

const props = defineProps({
  selectRow: {
    type: Array,
    required: true
  },
  actionTime: {
    type: String,
    required: true
  },
  actionType: {
    type: String,
    required: true
  }
});
interface FormItem {
  isShow: boolean;
  names: any[];
  ids: any[];
}
const formItem = reactive<FormItem>({
  isShow: false,
  names: [],
  ids: [],
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if(props.actionType == '删除') {
    alarmRulesDelete({
      names: formItem.names,
      ids: formItem.ids,
    })
    .then(res => {
      if (res.msg == 'ok') {
        ElMessage.success(props.actionType+'告警规则操作完成');
        emit('returnOK', 'refresh');
        formItem.isShow = false;
      }else {
        ElMessage.error(props.actionType+'告警规则操作失败');
      }
    }).catch((error) => {ElMessage.error(props.actionType+'告警规则操作失败')})
  }else {
    alarmRulesAction({
      names: formItem.names,
      ids: formItem.ids,
      status: props.actionType == '启用'?'enabled':'disabled',
    })
    .then(res => {
      if (res.msg == 'ok') {
        ElMessage.success(props.actionType+'告警规则操作完成');
        emit('returnOK', 'refresh');
        formItem.isShow = false;
      }else {
        ElMessage.error(props.actionType+'告警规则操作失败');
      }
    }).catch((error) => {ElMessage.error(props.actionType+'告警规则操作失败')})
  }
  
}
watch(
  ()=> props.actionTime,
  (val)=>{
    formItem.isShow = true;
    let ids:any[] = [];
    let names:any[] = [];
    props.selectRow.forEach((item:any) => {
      names.push(item.name);
      ids.push(item.id);
    });
    formItem.ids = ids;
    formItem.names = names;
  }
);
</script>