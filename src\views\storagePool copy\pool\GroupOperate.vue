<template>
	<el-dialog v-model="formItem.isShow" append-to-body :title="formItem.title"  class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="分组名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入分组名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { poolTreeNew,poolTreeEdit } from '/@/api/StoreManage'; // 接口
import { propName } from '/@/model/network'; // 表列、正则
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '',
	name: '',
  id: '',
  pid: ''
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['groupOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        formItem.isShow = false;
        if(formItem.title == '添加分组') {
          poolTreeNew({
            name: formItem.name,
            pid: formItem.id,
            remark: '',
          }).then((res) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              ElMessage.success(formItem.title+'操作完成');
              emit('groupOK', 'refresh');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }else {
          poolTreeEdit({
            name: formItem.name,
            id: formItem.id,
            remark: '',
          }).then((res) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              ElMessage.success(formItem.title+'操作完成');
              emit('groupOK', '');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }
			}
		});
	}
};

// 打开弹窗
const openDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
      console.log(row)

    if (row.type=='new') {
      if (ruleFormRef.value) {
        ruleFormRef.value.resetFields();
      }
      formItem.title = '添加分组'
      formItem.id = row.id
    }else {
      formItem.title = '修改分组'
      formItem.name = row.name
      formItem.id = row.id
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>