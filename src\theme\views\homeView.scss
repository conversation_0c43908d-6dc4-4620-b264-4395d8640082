.home-view {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;

  .top-stats {
    width: 100%;
    height: 200px;
    border-radius: 10px;
    background-color: #FFFFFF;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .stat-card {
      width: 390px;
      height: 160px;
      border-radius: 8px;
      position: relative;

      &:first-child {
        background-color: #E7EFFC;
      }

      &:nth-child(2) {
        background-color: #F5EFE9;
      }

      &:nth-child(3) {
        background-color: #E7EFFC;
      }

      &:last-child {
        background-color: #F5EFE9;
      }

      .stat-icon {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        width: 204px;
        height: 140px;

        img {
          width: 100%;
        }
      }

      .stat-content {
        margin-left: 20px;
        margin-top: 45px;

        .stat-number {
          height: 30px;
          line-height: 30px;
          font-size: 30px;
          color: #333333;
          font-weight: 500;
          margin-bottom: 23px;

          .unit {
            font-size: 16px;
            color: #333;
          }
        }

        .stat-label {
          font-family: Adobe Heiti Std;
          color: #333333;
          font-weight: 400;
          font-style: normal;
          font-size: 14px;
          height: 14px;
          line-height: 14px;
        }
      }
    }
  }

  .database-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    .db-card {
      width: 100%;
      height: 221px;
      padding: 20px;
      border-radius: 10px;
      background-color: #FFFFFF;

      .db-header {
        font-family: Adobe Heiti Std;
        color: #333333;
        font-weight: 500;
        font-size: 16px;
        height: 16px;
        line-height: 16px;
        margin-bottom: 17px;
      }

      .db-content {
        display: flex;
        justify-content: space-between;

        .db-left {
          display: flex;
          justify-content: space-between;
          flex: 1;
          padding-left: 20px;
          padding-right: 78px;
          padding-top: 43px;

          .db-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: Adobe Heiti Std;

            &:not(:last-child) {
              position: relative;

              &::after {
                content: '';
                position: absolute;
                right: -55px;
                top: 13px;
                width: 1px;
                height: 27px;
                background-color: #D8DDE8;
              }
            }

            .number {
              font-size: 22px;
              height: 22px;
              line-height: 22px;
              font-weight: 500;
              color: #333333;
              margin-bottom: 17px;

              .unit {
                font-size: 14px;
                color: #666;
              }
            }

            .label {
              color: #666666;
              font-weight: 400;
              font-size: 12px;
              height: 12px;
              line-height: 12px;
            }
          }
        }

        .db-right {
          width: 268px;
          height: 146px;
          border-radius: 8px;
          background-color: #FFF8F2;

          .storage-info {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .storage-info-left {
              padding-top: 14px;
              padding-left: 41px;

              :deep(.el-radio-group) {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                
                .el-radio {
                  align-items: center;
                }
              }

              .storage-item {
                width: 140px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .storage-item-text {
                  font-size: 13px;
                }

                .value {
                  font-size: 17px;
                  margin-left: 51px;
                }
              }
            }

            .progress-bar {
              margin-top: auto;
              height: 58px;
              background-color: #FFEEE3;
              padding-top: 16px;
              padding-left: 25px;
              padding-right: 23px;

              .progress-bar-text {
                display: flex;
                font-family: Adobe Heiti Std;
                color: #666666;
                font-weight: 400;
                font-size: 12px;
                height: 12px;
                line-height: 12px;
                margin-bottom: 10px;
              }

              :deep(.el-progress) {
                position: relative;
                
                .el-progress__text {
                  position: absolute;
                  top: -24px;
                  right: 0;
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }
  }
} 