/* 使用现代的 @use 和 @forward 语法替代 @import */
@use 'element-plus/dist/index.css' as *;
@use './app.scss' as *;
@use 'common/transition.scss' as *;
@use './other.scss' as *;
@use './media/media.scss' as *;
@use './waves.scss' as *;
@use './scss/index.scss' as *;

/* 对于 CSS 文件，仍然使用 @import，因为它们不是 SASS 模块 */
@import "./iconfont/defaulticonfont/iconfont.css";
@import "./iconfont/font_4448676_uhxk0qaod3s/iconfont.css";
@import "./iconfont/font_4513248_vo9q2co2hf/iconfont.css";
@import "./iconfont/font_4971620_uskxdz2z2t/iconfont.css";

/* 强制 SVG Symbol 颜色继承 */
.svg-symbol,
svg.svg-symbol {
  fill: currentColor !important;
}

.svg-symbol *,
svg.svg-symbol * {
  fill: inherit !important;
}

/* 确保 SVG symbol 中的所有 path 元素都继承颜色 */
symbol path {
  fill: currentColor !important;
}

/* 通过 use 元素使用的 SVG symbol */
use {
  fill: inherit !important;
}

