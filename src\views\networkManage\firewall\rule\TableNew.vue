<template>
	<el-dialog v-model="formItem.isShow" title="添加规则" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="规则" prop="ruleid">
				<el-select v-model="formItem.ruleid" style="width: 100%">
					<el-option v-for="(item,index) in ruleData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="描述">
				<el-input v-model="formItem.describe" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入安全组描述内容"/>
			</el-form-item>
			<el-form-item label="方向" prop="directionID">
				<el-select v-model="formItem.directionID" style="width: 100%">
					<el-option v-for="(item,index) in directionData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="打开端口" prop="openPortID" v-if="formItem.openduankou">
				<el-select v-model="formItem.openPortID" style="width: 100%">
					<el-option v-for="(item,index) in openPortData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="类型" prop="type" v-if="formItem.typeCode">
				<el-input v-model="formItem.type" type="number" placeholder="请输入-1至255的ICMP类型值范围" />
			</el-form-item>
			<el-form-item label="编码" prop="codes" v-if="formItem.typeCode">
				<el-input v-model="formItem.codes" type="number" placeholder="请输入-1至255的ICMP类型值范围" />
			</el-form-item>
			<el-form-item label="端口号" prop="portNumber" v-if="formItem.judgePort">
				<el-input v-model="formItem.portNumber" type="number" placeholder="请输入1至65534的整数" />
			</el-form-item>
			<el-form-item label="起始端口号" prop="startPort" v-if="formItem.startEnd">
				<el-input v-model="formItem.startPort" type="number" placeholder="请输入1至65534的整数" />
			</el-form-item>
			<el-form-item label="终止端口号" prop="endPort" v-if="formItem.startEnd">
				<el-input v-model="formItem.endPort" type="number" placeholder="请输入1至65534的整数" />
			</el-form-item>
			<el-form-item label="远程" prop="longRangeID">
				<el-select v-model="formItem.longRangeID" style="width: 100%">
					<el-option v-for="(item,index) in longRangeData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="CIDR" prop="cidr" v-if="formItem.judgelongRange">
				<el-input v-model="formItem.cidr" placeholder="例:192.168.1.0/24" />
			</el-form-item>
			<el-form-item label="安全组" prop="aqz" v-if="!formItem.judgelongRange">
				<el-select v-model="formItem.aqz" style="width: 100%">
					<el-option v-for="(item,index) in formItem.groupData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<!-- <el-form-item label="以太网类型" prop="ethernetID" v-if="!formItem.judgelongRange">
				<el-select v-model="formItem.ethernetID" style="width: 100%">
					<el-option v-for="(item,index) in ethernetData" :key="index" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item> -->
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { secureGroupQuery, ruleTableNew } from '/@/api/Network'; // 接口
import { ruleData, directionData, openPortData, longRangeData, ethernetData,propICMP,propPort,propSub } from '/@/model/network'; // 表列、正则

const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	ruleid: 'GZ_TCP', // 规则
	describe: '', // 描述
	directionID: 'enter', // 方向
	openPortID: 'all', // 打开端口
	type: '', // 类型
	codes: '', // 编码
	portNumber: '', // 端口
	startPort: '', // 起始端口号
	endPort: '', // 终止端口号
	longRangeID: 'cidr', // 远程
	aqz: '', // 安全组
	ethernetID: 'ipv4', // 以太网类型
	cidr: '', // CIDR
	groupData: [],
	openduankou: true,
	typeCode: false,
	judgePort: false,
	startEnd: false,
	judgelongRange: true,
	tableRow: {name: '', id: ''},
});
const rules = reactive<FormRules>({
	ruleid: [{ required: true, message: '必选项', trigger: 'blur' }],
	directionID:[ {required: true, message: '必选项', trigger: 'blur' }],
	openPortID:[ {required: true, message: '必选项', trigger: 'blur' }],
	type:[
		{required: true, message: '必填项', trigger: 'blur' },
		{ validator: propICMP, trigger: 'blur' },
	],
	codes: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propICMP, trigger: 'blur' },
	],
	portNumber: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' },
	],
	startPort: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' },
	],
	endPort: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' },
	],
	longRangeID:[ {required: true, message: '必选项', trigger: 'blur' }],
	cidr:[
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propSub, trigger: 'blur' },
	],
	aqz:[ {required: true, message: '必选项', trigger: 'blur' }],
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
	formItem.isShow = false;
	return emit('returnOK', 'refresh');
	// if (ruleFormRef.value) {
	// 	// 确保 ruleFormRef 已初始化
	// 	ruleFormRef.value.validate((val) => {
	// 		if (val) {
	// 			let	range_min = ''
	// 			let	range_max = ''
	// 			if(formItem.openPortID == "port"){
	// 				range_min = formItem.portNumber
	// 				range_max = formItem.portNumber
	// 			}else if(formItem.openPortID == "range"){
	// 				range_min = formItem.startPort
	// 				range_max = formItem.endPort
	// 			}
	// 			let rule = ''
	// 			if(formItem.ruleid == "GZ_TCP"||formItem.ruleid == "XY_TCP"){
	// 				rule = 'tcp'
	// 			}else if(formItem.ruleid == "GZ_UDP"||formItem.ruleid == "XY_UDP"){
	// 				rule = 'udp'
	// 			}else if(formItem.ruleid == "GZ_ICMP"){
	// 				rule = 'icmp'
	// 				range_min = formItem.type
  //         range_max = formItem.codes
	// 			}else if(formItem.ruleid == "XY_ICMP"){
	// 				rule = 'icmp'
	// 			}
	// 			let list = {
	// 				security_group_id: formItem.tableRow.id,
	// 				security_group_name: formItem.tableRow.name,
	// 				protocol: rule,
	// 				port_range_min: range_min,
	// 				port_range_max: range_max,
	// 				direction: formItem.directionID == "enter"?"ingress":"egress",
	// 				remote_group_id: formItem.longRangeID=="aqz"?formItem.aqz:'',
	// 				remote_ip_prefix: formItem.longRangeID=="cidr"?formItem.cidr:'',
	// 				description: formItem.describe
	// 			}
	// 			ruleTableNew(list).then((res) => {
	// 				if (res.msg == 'ok') {
	// 					formItem.isShow = false;
	// 					ElMessage.success('添加规则操作完成');
	// 					emit('returnOK', 'refresh');
	// 				} else {
	// 					ElMessage.error(res.msg);
	// 				}
	// 			});
	// 		}
	// 	});
	// }
};
// 导航数据获取
const getGroupData = () => {
	secureGroupQuery().then((res: any) => {
		formItem.groupData = res;
	});
};
watch(
	() => formItem.longRangeID,
	(val) => {
		if (val == 'aqz') {
			formItem.judgelongRange = false;
		} else {
			formItem.judgelongRange = true;
		}
	}
);
// 远程
watch(
	() => formItem.openPortID,
	(val) => {
		if (val == 'port') {
			formItem.judgePort = true;
			formItem.startEnd = false;
		} else if (val == 'range') {
			formItem.judgePort = false;
			formItem.startEnd = true;
		} else {
			formItem.judgePort = false;
			formItem.startEnd = false;
		}
	}
);
// 规则
watch(
	() => formItem.ruleid,
	(val) => {
		if (val == 'GZ_ICMP') {
			formItem.openduankou = false;
			formItem.typeCode = true;
			formItem.judgePort = false;
			formItem.openPortID = 'all';
		} else if (val == 'XY_ICMP' || val == 'XY_TCP' || val == 'XY_UDP') {
			formItem.openduankou = false;
			formItem.typeCode = false;
			formItem.judgePort = false;
			formItem.openPortID = 'all';
		} else {
			formItem.openduankou = true;
			formItem.typeCode = false;
		}
	}
);
// 规则
watch(
	() => formItem.ruleid,
	(val) => {
		if (val == 'GZ_ICMP') {
			formItem.judgelongRange = false;
		} else {
			formItem.judgelongRange = true;
		}
	}
);
// 打开弹窗
const ruleDialog = async (row:any) => {
  formItem.isShow = true;
	formItem.tableRow = row;
	formItem.describe = '';
	nextTick(() => {
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
		// getGroupData()
	});
};

// 暴露变量
defineExpose({
	ruleDialog,

});
</script>