<template>
  <!-- 替换图标上传文件使用 -->
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    :action="action"
    :multiple="multiple"
    :limit="limit"
    :on-exceed="handleExceed"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-remove="handleRemove"
    :accept="computedAccept"
    list-type="picture-card"
    :disabled="disabled"
    :headers="headers"
  >
    <slot>
      <el-icon><Plus /></el-icon>
    </slot>
  </el-upload>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus} from '@element-plus/icons-vue'

const props = defineProps({
  // 上传的地址
  action: {
    type: String,
    required: true
  },
  // 默认已上传的文件列表
  modelValue: {
    type: Array,
    default: () => []
  },
  // 是否支持多选文件
  multiple: {
    type: Boolean,
    default: true
  },
  // 最大允许上传个数
  limit: {
    type: Number,
    default: 0
  },
  // 文件大小限制(MB)
  fileSize: {
    type: Number,
    default: 0
  },
  // 接受上传的文件类型数组
  fileType: {
    type: Array,
    default: () => []
  },
  // 提示文字
  tip: {
    type: String,
    default: ''
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
})

const uploadRef = ref(null);
// 暴露方法供父组件调用
const openFileDialog = () => {
  uploadRef.value?.$el.querySelector('input[type="file"]').click();
};

// 暴露方法给父组件，可以使用ref来调用这个方法打开上传组件
defineExpose({
  openFileDialog
});
const headers = ref({
  'Token': window.sessionStorage.getItem('vrts-token')
})
const emit = defineEmits(['update:modelValue', 'success', 'error', 'remove'])

const fileList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 将数组格式的 fileType 转换为字符串格式供 el-upload 使用
const computedAccept = computed(() => {
  return props.fileType.join(',')
})

// 格式化显示可接受的文件类型
const formatAcceptTypes = () => {
  return props.fileType.map(type => {
    if (type.startsWith('.')) {
      return type.toUpperCase()
    }
    return type
  }).join('、')
}

// 文件超出数量限制时的钩子
const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
}

// 上传前的校验
const beforeUpload = (file) => {
  // 校验文件类型
  if (props.fileType && props.fileType.length > 0) {
    const fileExtension = file.name.split('.').pop().toLowerCase()
    const isValidType = props.fileType.some(type => {
      // 处理类似 ".pdf" 这样的扩展名
      if (type.startsWith('.')) {
        return fileExtension === type.substring(1).toLowerCase()
      }
      // 处理类似 "image/*" 这样的MIME类型
      else if (type.includes('/*')) {
        const mainType = type.split('/*')[0]
        return file.type.startsWith(mainType)
      }
      // 处理完整的MIME类型
      else {
        return file.type === type
      }
    })
    
    if (!isValidType) {
      ElMessage.warning(`只能上传 ${formatAcceptTypes()} 格式的文件`)
      return false
    }
  }
  
  // 校验文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      ElMessage.warning(`上传文件大小不能超过 ${props.fileSize}MB!`)
      return false
    }
  }
  
  return true
}

// 文件上传成功时的钩子
const handleSuccess = (response, uploadFile, uploadFiles) => {
  fileList.value = uploadFiles
  emit('success', response, uploadFile, uploadFiles)
}

// 文件上传失败时的钩子
const handleError = (error, uploadFile, uploadFiles) => {
  ElMessage.error('上传失败')
  emit('error', error, uploadFile, uploadFiles)
}

// 文件移除时的钩子
const handleRemove = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles
  emit('remove', uploadFile, uploadFiles)
}
</script>

<style scoped>
  :deep(.el-upload-list)  {
    width: 0;
    height: 0;
  }
  :deep(.el-upload) {
    height: 0;
  }
  :deep(.el-upload--picture-card) {
    border: none !important;
  }
</style>