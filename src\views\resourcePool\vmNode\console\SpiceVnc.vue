<template>
  <div class="spice-iframe-container">
    <iframe
      ref="spiceIframeRef"
      :src="state.iframeSrc"
      frameborder="0"
      class="spice-iframe"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick, ref } from 'vue';
import { ElMessage } from 'element-plus';
// 定义变量内容
const state = reactive({
  iframeSrc: ''
});

const spiceIframeRef = ref<HTMLIFrameElement>();
const emit = defineEmits(['returnOK', 'spice-ready']);
// 构建SPICE URL的函数，将Host和Port作为URL参数传递
const buildSpiceUrl = (host: string, port: string) => {
  const baseUrl = '/spice-html5/spice.html';
  const params = new URLSearchParams({
    host: host,
    port: port,
    autoconnect: '1',  // 启用自动连接
    auto: '1',
    connect: 'auto',
    resize: '1',
    clipboard: '1',
    timestamp: Date.now().toString()
  });

  return `${baseUrl}?${params.toString()}`;
};

// 打开弹窗并设置连接信息 - 自动填充Host和Port并触发连接
const openDialog = async (website: string, port: string) => {
  // 构建包含Host和Port参数的URL
  const spiceUrl = buildSpiceUrl(website, port);
  await nextTick(() => {
    // 设置iframe源，URL参数会自动传递给spice.html
    state.iframeSrc = spiceUrl;
  });
};
// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.spice-iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  .spice-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #f5f7fa;
  }
}
</style>