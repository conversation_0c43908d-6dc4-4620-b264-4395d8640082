<template>
  <div class="store-area">
      <div class="page-btn-area">
         <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
            <el-button type="primary" plain @click="resetClick(state.tableSelect)" v-if="powerItem.chongshe">重设分组</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
          </div>
          <div v-if="powerItem.tianjia">
            <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area" v-if="powerItem.liebiao">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<template #vlanid="{ row }">
              <span>{{ row.vlanid?row.vlanid:'-' }}</span>
						</template>
            <!-- 关联主机数 -->
						<template #hosts="{ row }">
              <el-tooltip effect="dark" :disabled="row.hosts==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.hosts" >
                      <el-table-column prop="cluster_name" label="集群" />
                      <el-table-column prop="hostname" label="物理机" />
                      <el-table-column prop="ip" label="主机IP" />
                    </el-table>
                  </div>
                </template>
                <el-tag :type="row.hosts?.length>0?'success':'info'">{{ row.hosts?.length }}</el-tag>
              </el-tooltip>
						</template>
            <!-- 存储资源 -->
						<template #targets="{ row }">
              <el-tooltip effect="dark" :disabled="row.targets==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.targets" >
                      <el-table-column prop="target_name" label="IQN名称" />
                    </el-table>
                  </div>
                </template>
                <el-tag :type="row.targets?.length>0?'success':'info'">{{ row.targets?.length }}</el-tag>
              </el-tooltip>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.xiugai || powerItem.shanchu">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <span v-else>-</span>
						</template>
          </my-table>
        </div>
      </div>
		<TableReset ref="resetRef" @returnOK="returnOK" />
		<TableEdit ref="editRef" @returnOK="returnOK" />
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search,ArrowDownBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import { distributedColumns } from '/@/model/storeManage'; // 表列、正则
import { distributedTableQuery,distributedTableDelete } from '/@/api/StoreManage'; // 接口
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const TableReset = defineAsyncComponent(() => import('./TableReset.vue'));
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

// 定义变量内容
const state = reactive({
  columns: distributedColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  deleteTime: '',
  groupID: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(!true){
    let list = ['aaa','bbb','ccc','ddd','eee','fff']
		return {
			data: [
        {name: list[Math.round(Math.random() * 5)],ip:'***********:3206',share:false,id:'aa1'},
        {name: list[Math.round(Math.random() * 5)],ip:'**********:3206',share:true,id:'aa2'}
      ], // 数据
			total: 2, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    distributedTableQuery({
      group_id: state.groupID,
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 重设分组
const resetRef = ref();
const resetClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    resetRef.value.resetDialog(arr)
  }
}
const editRef = ref();
// 表操作列
const commandItem = (item: string,row:any)=>{
  switch (item) {
    case 'bj':
      editRef.value.editDialog(row)
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '存储资源/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    distributedTableDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        setTimeout(()=>{
          refresh()
          ElMessage.success('删除操作完成');
        },1000)
      }else {
        ElMessage.error('删除操作失败');
      }
    })
  }else {
    refresh()
  }
}
const openDialog = async (row: any) => {
  state.groupID = row.id
  nextTick(() => {
		if(tableRef.value){
			refresh();
		}
  })
}
// 暴露变量
defineExpose({
	openDialog,
});
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  chongshe: false,
  xiugai: false,
  saomiao: false,
  shanchu: false,
  tianjia: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'fenbushicunchuchiliebiao',
    'fenbushicunchuchisousuo',
    'cunchuchichongshefenzu',
    'fenbushicunchuchixiugai',
    'fenbushicunchuchishanchu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.fenbushicunchuchiliebiao;
		powerItem.sousuo = res.data.fenbushicunchuchisousuo;
		powerItem.chongshe = res.data.cunchuchichongshefenzu;
		powerItem.xiugai = res.data.fenbushicunchuchixiugai;
		powerItem.shanchu = res.data.fenbushicunchuchishanchu;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style scoped lang="scss">
.store-area {
  padding-top: 15px;
	width: calc(100%);
	height: calc(100%);
  .page-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.tooltip-table-area {
  width: 450px;
}
</style>