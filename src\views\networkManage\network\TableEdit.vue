<template>
	<el-dialog v-model="formItem.isShow" append-to-body title="修改网络" class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="网络名称">
				<el-input v-model="formItem.name" placeholder="请输入网络新名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { netEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/resource.js'; // 表列、正则
const props = defineProps({
	editTime: {
		type: String,
		required: true,
	},
  tableRow: {
    type: Object,
    required: true,
  }
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	name: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				netEdit({
          name: formItem.name,
				}).then((res) => {
					if(res.msg == 'ok') {
        		ElMessage.success('修改网络操作完成');
						emit('returnOK', 'refresh');
					}else {
        		ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};
watch(
	() => props.editTime,
	(val) => {
		formItem.isShow = true;
		formItem.name = props.tableRow.name
	}
);
</script>