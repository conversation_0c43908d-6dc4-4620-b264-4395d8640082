<template>
    <base-dialog v-model="dialogVisible" :title="title" @confirm="handleConfirm" @cancel="handleCancel">
      <div>我将要在这里写业务或一些自定义事件</div>
    </base-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import BaseDialog from '../BaseDialog/index.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(props.modelValue)
const title = ref('标题')

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val)
})

const handleConfirm = () => {
  // 这里可以添加业务逻辑
  
  emit('update:modelValue', false)
}

const handleCancel = () => {
  // 这里可以添加取消时的业务逻辑
  
  emit('update:modelValue', false)
}
</script> 