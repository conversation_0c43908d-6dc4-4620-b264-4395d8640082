import request from '/@/utils/request';
// 物理机 基本数据
export const physicsBasicQuery = () => {
	return request({
		url: '/theapi/v1/all/clusters',
		method: 'get',
	});
};
// 物理机 CPU趋势
export const physicsCpuQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/host/cpu',
		method: 'post',
		data,
	});
};
// 物理机 内存趋势
export const physicsMemQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/host/mem',
		method: 'post',
		data,
	});
};
// 物理机 磁盘趋势
export const physicsDiskQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/host/disk',
		method: 'post',
		data,
	});
};
// 物理机 网络趋势
export const physicsNetQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/host/network',
		method: 'post',
		data,
	});
};

// 虚拟机 基本数据
export const virtualBasicQuery = () => {
	return request({
		url: '/theapi/v1/monitor/vm/home',
		method: 'get',
	});
};
// 虚拟机 CPU趋势
export const virtualCpuQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/vm/cpu',
		method: 'post',
		data,
	});
};
// 虚拟机 内存趋势
export const virtualMemQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/vm/mem',
		method: 'post',
		data,
	});
};
// 虚拟机 磁盘趋势
export const virtualDiskQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/vm/disk',
		method: 'post',
		data,
	});
};
// 虚拟机 网络趋势
export const virtualNetQuery = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/vm/network',
		method: 'post',
		data,
	});
};
// 存储 基本数据
export const storageBasicQuery = () => {
	return request({
		url: '/theapi/v1/monitor/storage/base',
		method: 'get',
	});
};
// 存储 节点信息
export const storageNodeQuery = () => {
	return request({
		url: '/theapi/v1/ceph/osd',
		method: 'get',
	});
};
// 存储 集群 存储使用率
export const storageClusterStorage = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/storage/capacity',
		method: 'post',
		data,
	});
};
// 存储 集群 内存使用率
export const storageClusterMemory = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/storage/mem',
		method: 'post',
		data,
	});
};
// 存储 集群 IOPS
export const storageClusterIOPS = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/storage/iops',
		method: 'post',
		data,
	});
};
// 存储 集群 吞吐量
export const storageClusterHandlingCapacity = (data: object) => {
	return request({
		url: '/theapi/v1/monitor/storage/throughput',
		method: 'post',
		data,
	});
};
