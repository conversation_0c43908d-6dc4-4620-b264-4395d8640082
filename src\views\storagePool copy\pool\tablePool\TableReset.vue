<template>
	<el-dialog v-model="formItem.isShow" append-to-body :title="formItem.title"  class="dialog-500">
		<div>
      <span>是否对下列存储资源重设分组？</span>
      <p class="table-names">{{ formItem.tableNames.toString() }}</p>
      <div class="tree-area">
        <h4>当前选择: {{ formItem.treeName }}</h4>
        <el-tree
          ref="treeRef"
          node-key="id"
          :highlight-current="true"
          :default-expand-all="true"
          :expand-on-click-node="false"
          :data="formItem.treeData"
          :props="defaultProps"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span style="display: flex; align-items: center">
              <el-icon v-if="data.icon"><component :is="data.icon" /></el-icon>
              <span style="margin-left: 8px">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { poolTreeQuery,resetGroupQuery } from '/@/api/StoreManage'; // 接口
const formItem = reactive({
	isShow: false,
  title: '',
  treeData: [],
	treeName: '',
  treeID: '',
  treePID: '',
  tableNames: [''],
  tableIDs: [''],
  
});
const defaultProps = {
	children: 'children',
	label: 'name',
};
interface Tree {
	name: string;
	id: string;
	pid: string;
	icon?: any;
	children?: Tree[];
}
const handleNodeClick = (data: Tree) => {
	formItem.treeID = data.id
	formItem.treePID = data.pid
	formItem.treeName = data.name;
};
const treePoolQuery = async () => {
	formItem.treeData = []
	poolTreeQuery().then((res:any) => {
		formItem.treeData = res.data
	})
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
  formItem.isShow = false;
  resetGroupQuery({
    group_id: formItem.treeID,
    group_name: formItem.treeName,
    pool_ids : formItem.tableIDs,
    pool_names : formItem.tableNames,
  }).then((res) => { 
    if(res.msg == 'ok') {
      ElMessage.success('存储资源操作完成');
      emit('returnOK', 'refresh');
    }else {
      ElMessage.error(res.msg);
    }
  })
};

// 打开弹窗
const resetDialog = async (arr: any) => {
	formItem.isShow = true;
  formItem.tableNames = []
  formItem.tableIDs = []
	nextTick(() => {
    treePoolQuery()
    formItem.title = '重设分组'
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formItem.tableNames = names
    formItem.tableIDs = ids
	});
};
// 暴露变量
defineExpose({
	resetDialog,
});
</script>
<style lang="scss" scoped>
  .tree-area {
    width: 100%;
    height: 200px;
    border: 1px solid #ccc;
    h4 {
      padding: 10px;
      color: green;
    }
  }
  .table-names {
    font-weight: 600;
    word-wrap: break-word;
    padding: 10px 0;
  }
</style>