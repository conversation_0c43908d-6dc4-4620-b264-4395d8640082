<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
        <el-button type="primary" plain @click="addClick" v-if="powerItem.tianjia">添加快照</el-button>
        <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
      </div>
      <div v-if="powerItem.sousuo">
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area" v-if="powerItem.liebiao">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
        <template #vm_name="{row}">
          <span>{{ props.treeItem.name }}</span>
        </template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.xiugai || powerItem.shanchu || powerItem.huanyuan">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item> -->
                <el-dropdown-item command="hy" v-if="powerItem.huanyuan">还原虚拟机</el-dropdown-item>
                <el-dropdown-item command="sc" style="color:red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <span v-else>-</span>
        </template>
			</my-table>
    </div>
    <TableNewEdit ref="newEditRef" @returnOK="returnOK" />
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search,ArrowDownBold } from '@element-plus/icons-vue'
import { dayjs,ElMessage,ElMessageBox  } from 'element-plus';
import { vmSnapshotQuery,vmSnapshotDelete,vmSnapshotRestore } from '/@/api/ResourcePool/vm'; // 接口
import { snapshotColumns } from '/@/model/resource'; // 表列、正则

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNewEdit = defineAsyncComponent(() => import('./TableNewEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  tableSearch: '',
  columns: snapshotColumns as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    vmSnapshotQuery({
      vm_id: props.treeItem.id,
      vm_name: props.treeItem.name,
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 添加/修改快照
const newEditRef = ref();
const addClick = ()=>{ 
  newEditRef.value.openDialog('new',props.treeItem);
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  switch (item) {
    case 'bj':
      newEditRef.value.openDialog('edit',props.treeItem,row);
      break;
    case 'hy':
      ElMessageBox.confirm(
        `是否将快照 ${row.name} 进行 还原虚拟机 操作?`,
        '还原虚拟机',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        vmSnapshotRestore({
          vm_id: props.treeItem.id,
          vm_name: props.treeItem.name,
          snapshot_name: row.name,
        }).then((res:any) => {
          if(res.msg == 'ok'){
            ElMessage.success('还原虚拟机快照操作完成');
          }else {
            ElMessage.error('还原虚拟机快照操作失败');
          }
        })
      }).catch(() => {})
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 删除存储池
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '虚拟机快照/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    vmSnapshotDelete({
      vm_id: props.treeItem.id,
      vm_name: props.treeItem.name,
      snapshot_names: formDelet.tableNames,
    })
    .then((res:any) => {
      if(res.msg == 'ok'){
        setTimeout(() => {
          refresh()
        }, 500);
        ElMessage.success('删除虚拟机快照操作完成');
      }else {
        ElMessage.error('删除虚拟机快照操作失败');
      }
    })
  }else {
    refresh()
  }
}
const tableRef = ref();
// 刷新
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
watch(
  ()=> props.treeItem,
  (val)=>{
    refresh()
  }
);
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
  huanyuan: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'xunijikuaizhaoliebiao',
    'xunijikuaizhaosousuo',
    'tianjiaxunijikuaizhao',
    'xiugaixunijikuaizhao',
    'shanchuxunijikuaizhao',
    'kuaizhaohuanyuanxuniji',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.xunijikuaizhaoliebiao;
		powerItem.sousuo = res.data.xunijikuaizhaosousuo;
		powerItem.tianjia = res.data.tianjiaxunijikuaizhao;
		powerItem.xiugai = res.data.xiugaixunijikuaizhao;
		powerItem.shanchu = res.data.shanchuxunijikuaizhao;
		powerItem.huanyuan = res.data.kuaizhaohuanyuanxuniji;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>