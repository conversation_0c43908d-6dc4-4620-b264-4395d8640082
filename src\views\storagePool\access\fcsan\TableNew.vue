<template>
	<el-dialog v-model="formItem.isShow" title="添加存储池" append-to-body class="dialog-700">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储池名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
			<el-form-item label="使用方式">
				<el-radio-group v-model="formItem.way">
					<el-radio value="share">共享存储</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="存储设备" prop="device">
				<el-select v-model="formItem.device" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" placeholder="请选择分布式交换机">
					<el-option v-for="item in formItem.deviceData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="OCFS2心跳网络" prop="ocfs2" v-if="formItem.way == 'share'">
				<el-radio-group v-model="formItem.ocfs2">
					<el-radio value="mange">管理网络</el-radio>
					<el-radio value="storage">存储网络</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="分布式交换机" prop="switch" v-if="formItem.ocfs2 == 'storage'">
				<el-select v-model="formItem.switch" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" placeholder="请选择分布式交换机">
					<el-option v-for="item in formItem.switchData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="选择主机">
				<div class="ztree-publick">
					<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes"></ZtreePublick>
				</div>
			</el-form-item>
      <el-form-item label="已选存储资源" prop="storage">
				<el-input v-model="formItem.storage" disabled/>
			</el-form-item>
		</el-form>
		<div class="table-area">
			<my-table ref="tableRef" :pagination="formItem.pagination" :columns="formItem.columns" :request="getTableData" @selectionChange="selectChange">
			</my-table>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { storeDocking,storageResourcesQuery } from '/@/api/StoreManage/index'; // 接口
import { portGroupEdit,hostAllQuery } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network'; // 表列、正则
import { fcSorageColumns } from '/@/model/storeManage'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	columns: fcSorageColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
  tableID: [''],
	name: '',
  way: 'share',
	device: '',
	deviceData: [{ name: '主机1', id: '1111111' }],
  ocfs2: 'mange',
  switch: '',
  storage: '',
  switchData: [{ name: '主机1', id: '1111111' }],
  selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'change' },
	],
	ocfs2: [{ required: true, message: '必选项', trigger: 'blur' }],
	switch: [{ required: true, message: '必选项', trigger: 'change' }],
	storage: [{ required: true, message: '必选项', trigger: 'change' }],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0' },
		{ id: '2', name: '主机池1', pid: '1' },
		{ id: '3', name: '集群1', pid: '2' },
		{ id: '4', name: '主机1', pid: '3' },
		{ id: '5', name: '主机2', pid: '3' },
		{ id: '6', name: '主机3', pid: '3' },
		{ id: '7', name: 'vm1', pid: '4' },
		{ id: '8', name: 'vm2', pid: '4' },
		{ id: '9', name: 'vm3', pid: '5' },
		{ id: '11', name: 'vm4', pid: '5' },
		{ id: '12', name: 'vm5', pid: '5' },
		{ id: '13', name: 'vm3', pid: '5' },
		{ id: '14', name: 'vm3', pid: '5' },
		{ id: '31', name: 'aaaaa', pid: '5' },
		{ id: '32', name: 'vm4', pid: '5' },
		{ id: '63', name: 'vm2', pid: '5' },
	];
}

// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
		setTimeout(() => {
			formItem.type = 'FCsan-添加'
			treeData()
		}, 200);
    
	});
};
// 分布式交换机
const hostColnyQuery = ()=>{
  let hostData: any[] = []
  hostAllQuery()
  .then(res => {
    res?.forEach((em:any) => {
      hostData.push({
        name: em.name, 
        id: em.id
      })
    });
    formItem.switchData = hostData
  })
}
// 获取表数据
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  formItem.tableSelect = []
  if(true) {
    return {
      data: [{name: '测试1'}], // 数据
      total: 1 // 总数
    }
  }
	return new Promise(async(resolve)=>{
    storageResourcesQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: formItem.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 表格选中变化
const selectChange = (row: any)=>{
  formItem.tableSelect = row
  let names:any[] = [];
  let ids:any[] = [];
  row.forEach((item:any)=>{
    names.push(item.name);
    ids.push(item.id);
  })
  formItem.storage = names.toString()
  formItem.tableID = ids
}
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				portGroupEdit({
					name: formItem.name,
				}).then((res: any) => {
					emit('returnOK', 'refresh');
				});
			}
		});
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.table-area {
	height: 200px;
	width: 100%;
	position: relative;
}
.ztree-publick {
	height: 300px;
}
</style>