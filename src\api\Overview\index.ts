import request from '/@/utils/request';

// 获取首页基本信息
export const homeDefaultQuery = () => {
	return request({
		url: '/theapi/v2/home',
		method: 'get',
	})
};
// 大屏 机器数量
export const largeScreenMachineNumber = () => {
	return request({
		url: '/theapi/v1/screen/nodes',
		method: 'get',
	})
};
export const alarmDataQuery = () => {
	return request({
		url: '/theapi/v2/alerts/all',
		method: 'get',
	})
};
// 资源使用接口
export const getResourceInfo = () => {
	return request({
		url: '/vtl/status',
		method: 'get',
	}) as Promise<RequestResult<resourceInfo>>;
};

/**
 * 获取磁盘信息
 * @param params
 * @returns
 */
export const getDiskInfo = () => {
	return request({
		url: '/vtl/disk',
		method: 'get',
	}) as Promise<RequestResult<Array<DiskInfo>>>;
};
