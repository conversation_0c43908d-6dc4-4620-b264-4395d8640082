const fs = require('fs');
const path = require('path');

// 递归查找所有 .vue 文件
function findVueFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findVueFiles(filePath));
      }
    } else {
      const ext = path.extname(file);
      if (ext === '.vue') {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的 commandItem 函数参数类型问题
function fixCommandItemInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复 commandItem 函数的参数类型
    if (content.includes('commandItem') && content.includes(':never')) {
      content = content.replace(/commandItem\s*=\s*\([^)]*row\s*:\s*never[^)]*\)/g, (match) => {
        return match.replace(':never', ':any');
      });
      modified = true;
    }
    
    // 修复 operateItem 函数的参数类型
    if (content.includes('operateItem') && content.includes(':never')) {
      content = content.replace(/operateItem\s*=\s*\([^)]*row\s*:\s*never[^)]*\)/g, (match) => {
        return match.replace(':never', ':any');
      });
      modified = true;
    }
    
    // 修复其他类似的函数参数类型
    if (content.includes(':never') && (content.includes('row') || content.includes('item'))) {
      // 修复函数参数中的 never 类型
      content = content.replace(/(\w+)\s*:\s*never/g, '$1: any');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed commandItem in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding Vue files to fix commandItem...');
  
  const files = findVueFiles(srcDir);
  console.log(`Found ${files.length} Vue files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixCommandItemInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed commandItem in ${fixedCount} files`);
}

main();
