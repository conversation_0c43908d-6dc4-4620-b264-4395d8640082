<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="删除主机"
    class="dialog-500"
  >
    <div>
      <span>是否删除下列主机？</span>
      <p style="color:red;word-wrap: break-word">{{props.treeItem.name}}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { treeDeleteHost } from '/@/api/ResourcePool'; // 接口

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  deleteTime: {
    type: String,
    required: true
  }
});
const formItem = reactive({
  isShow: false,
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.isShow = false;
  treeDeleteHost({
    name: props.treeItem.name,
    id: props.treeItem.id
  })
  .then(res => {
    emit('returnOK', 'delete');
  })
}
watch(
  ()=> props.deleteTime,
  (val)=>{
    formItem.isShow = true;
    
  }
);
</script>