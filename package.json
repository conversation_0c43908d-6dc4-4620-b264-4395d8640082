{"name": "vue3_default", "version": "1.0.1", "description": "vue3基础框架", "author": "", "license": "", "type": "module", "scripts": {"dev": "vite --mode dev", "debug": "vite --mode dev --debug", "build": "vue-tsc --noEmit --skipLibCheck && vite build", "build:no-check": "vite build", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.ts,.tsx ./src --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,.ts,.tsx ./src --fix", "type-check": "vue-tsc --noEmit", "type-check:watch": "vue-tsc --noEmit --watch", "hot-reload": "node hot-reload.js"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@monaco-editor/loader": "^1.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "amfe-flexible": "^2.2.1", "axios": "^1.6.7", "countup.js": "^2.8.0", "cropperjs": "^1.6.1", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "element-plus": "2.9.7", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "js-table2excel": "^1.0.3", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.2", "relation-graph": "^2.1.35", "screenfull": "^6.0.2", "sortablejs": "^1.15.2", "splitpanes": "^3.1.5", "swiper": "^11.0.7", "uuid": "^9.0.1", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.7", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.9.1", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nabla/vite-plugin-eslint": "^2.0.5", "@types/jquery": "^3.5.32", "@types/js-cookie": "^3.0.6", "@types/js-md5": "^0.8.0", "@types/node": "^20.11.30", "@types/nprogress": "^0.2.3", "@types/postcss-pxtorem": "^6.0.3", "@types/qs": "^6.9.12", "@types/sortablejs": "^1.15.8", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.21", "code-inspector-plugin": "^0.20.6", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "postcss-pxtorem": "^6.1.0", "prettier": "^3.2.5", "sass": "^1.72.0", "typescript": "^5.4.3", "unplugin-vue-components": "^28.8.0", "vite": "^5.2.2", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^2.0.7"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}