<template>
	<el-dialog v-model="formItem.isShow" append-to-body class="dialog-500">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title"
				><span style="color: green">{{ formItem.name }}</span> 发送终止信号</span
			>
		</template>
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto" class="demo-ruleForm" status-icon>
			<el-form-item label="终止信号" prop="signal">
				<el-input v-model="formItem.signal" placeholder="请输入要发送的终止信号" />
			</el-form-item>
		</el-form>
    <p style="color:#ccc">
      提示：发送给容器的信号:整数或类似SIGINT的字符串。如果没有设置，则设置SIGKILL作为默认值，容器将退出。支持的信号因平台而异。此外，您可以省略“SIG前缀”。
    </p>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { containerKill } from '/@/api/ResourcePool/container'; // 接口
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	id: '',
	name: '',
	signal: '',
});
const propSignal = (rule:any, value:any, callback:any) => {
  if (value!== '') {
    callback()
  } else {
    callback(new Error("必填项"))
  }
}
const rules = reactive<FormRules>({
  signal: [
    { required: true, message: '必填项' },
    { validator: propSignal, trigger: "blur" },
  ],
});

const emit = defineEmits(['returnOK']);
const confirm = () => {
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        containerKill({
          id: formItem.id,
          name: formItem.name,
          signal: formItem.signal,
          })
        .then(res => {
          ElMessage.success('发送终止信号操作已完成')
          emit('returnOK', 'refresh');
        })
        .catch(err => {
          ElMessage.error('发送终止信号操作失败')
        })
      }
    })
  }
};
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		formItem.isShow = true;
		formItem.name = row.name;
		formItem.id = row.id;
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>

</style>