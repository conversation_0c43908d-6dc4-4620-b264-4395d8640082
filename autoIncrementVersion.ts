import { Plugin } from 'vite'
import fs from 'fs'
import path from 'path';
//版本号自增
const incrementVersion = (version: string) => {
  const parts = version.split('.').map(Number);
  parts[2]++;
  if (parts[2] > 9) {
    parts[2] = 0;
    parts[1]++;
    if (parts[1] > 9) {
      parts[1] = 0;
      parts[0]++;
    }
  }
  return parts.join('.');
}

export default function autoIncrementVersion(): Plugin {
  return {
    name: 'vite:autoIncrementVersion',
    apply: 'build',
    //构建开始时的钩子
    buildStart(options) {
      if (options) {
        try {
          const pkgPath = path.resolve("./public/version.json");
          console.log('Reading version.json from:', pkgPath);
          const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
          console.log('Current version:', pkg.version);
          pkg.version = incrementVersion(pkg.version);
          console.log('New version:', pkg.version);
          fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
        } catch (error) {
          console.error('Error updating version:', error);
        }
      }
    },
  }
} 