<template>
	<div class="task-statistics">
		<my-echarts :options="state.chartOption"></my-echarts>
	</div>
</template>
<script setup lang="ts" name="EchartsHealth">
import { defineAsyncComponent, reactive, onMounted, ref, Ref, inject, watch } from 'vue';
const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));
import * as echarts from 'echarts';

const props = defineProps({
	healthState: {
		type: String,
		required: true,
	},
});
const getOption = () => {
	return {
		title: [
			{
				text: '{name|' + '健康状态：' + '}{status|' + props.healthState + '}',
				bottom: '0%',
				left: 'center',
				textStyle: {
					rich: {
						name: {
							fontSize: '14',
							color: '#333',
						},
						status: {
							fontSize: '14',
							color: props.healthState == '正常' ? '#0caf1f' : '#f11e1e',
						},
					},
				},
				triggerEvent: true,
			},
		],
		grid: [
			{
				show: false,
				backgroundColor: 'rgba(0,0,0,0)',
				borderWidth: 0,
				y: 'bottom',
			},
		],
		series: [
			{
				type: 'gauge',
				startAngle: 180,
				endAngle: 0,
				min: 0,
				max: 2,
				radius: '100%',
				center: ['50%', '75%'],
				axisLine: {
					show: true,
					lineStyle: {
						width: -15,
						shadowBlur: 2,
						color: [
							[0, 'transparent'],
							[0.5, '#3367ff'],
							[1, '#BCBEC2'],
						],
					},
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				pointer: {
					width: '5%',
					length: '80%',
					color: 'black',
				},
				itemStyle: {
					normal: {
						color: '#70b4f7',
						shadowBlur: 2,
					},
				},
				title: {
					show: false,
				},
				detail: {
					show: false,
				},
				data: [{ value: props.healthState == '正常' ? 0.5 : 1.5, name: props.healthState }],
			},
		],
	};
};
// 定义变量内容
const state = reactive<EmptyObjectType>({
	chartOption: getOption(),
});

// 页面加载时
onMounted(() => {
	window.addEventListener('resize', () => {
		state.chartOption = getOption();
	});
});
watch(
	() => props.healthState,
	(val) => {
		state.chartOption = getOption();
	}
);
</script>
<style scoped lang="scss">
.task-statistics {
	height: calc(100%);
}
</style>
