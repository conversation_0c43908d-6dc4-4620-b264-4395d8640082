<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <el-button type="warning" round @click="returnClick"><el-icon><DArrowLeft /></el-icon> 返回</el-button>
          <el-button type="primary" plain @click="refresh">刷新</el-button>
          <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 类型 -->
						<template #type="{ row }">
							<el-tag :type="row.type == 'enabled'?'success':'info'">{{row.type == 'enabled'?'启用':'禁用'}}</el-tag>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
            	<el-button type="danger" @click="deleteClick([row])">删除</el-button>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
		<!-- <TableDelete :ignoreRow='state.ignoreRow' :ignoreTime='state.ignoreTime' ignoreType='集群' @returnOK="returnOK"></TableDelete> -->
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { DArrowLeft } from '@element-plus/icons-vue'
import { localColumns } from '/@/model/storeManage'; // 表列、正则
import { clusterAlarmQuery,clusterAlarmIgnore } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
// const TableDelete = defineAsyncComponent(() => import('./generalPublic/TableDelete.vue'));

// 定义变量内容
const state = reactive({
  columns: localColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
  selectRow: [],
  deleteTime: '',
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(true){
    let libs = ['AA','BB','CC','DD','EE','FF']
    let libA = ['enabled','enabled','disabled','disabled','enabled','disabled']
    const list = new Array(page.pageSize).fill({}).map((item, index) => {
			item = {
        name: index+libs[Math.round(Math.random() * 5)],
				type: libA[Math.round(Math.random() * 5)],
			};
			return item;
		});
		return {
			data: list, // 数据
			total: 100, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    clusterAlarmQuery().then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 忽略
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.selectRow = arr
    state.deleteTime = ''+new Date()
  }
}
// 返回
const returnClick = ()=>{
  router.push('/StoreManage');
}
// 返回数据
const returnOK = (item:any)=>{
  refresh()
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;

    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
