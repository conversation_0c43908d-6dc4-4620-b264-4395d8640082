<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否还原下列{{ props.restoreTime.split('/')[0] }}？</span>
      <p style="color:green;word-wrap: break-word">{{ props.names.toString() }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';

const props = defineProps({
  names: {
    type: Array,
    required: true
  },
  restoreTime: {
    type: String,
    required: true
  }
});
const formItem = reactive({
  isShow: false,
  title: '还原',
});

const emit = defineEmits(['restoreOK']);
const confirm =()=>{
  emit('restoreOK', 'delete');
  formItem.isShow = false;
}
watch(
  ()=> props.restoreTime,
  (val)=>{
    formItem.isShow = true;
    formItem.title = '还原'+val.split('/')[0]
  }
);
</script>