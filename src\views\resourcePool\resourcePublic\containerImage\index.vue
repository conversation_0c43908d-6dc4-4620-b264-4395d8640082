<template>
	<div class="resource-pool-container">
		<div class="tabs-btn-area">
			<div>
				<el-button type="primary" plain @click="refresh">刷新</el-button>
				<el-button type="primary" plain @click="pullClick">拉取容器镜像</el-button>
				<el-button type="primary" plain @click="uploadClick">上传容器镜像</el-button>
				<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
			</div>
			<div>
				<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
					<template #append>
						<el-button :icon="Search" @click="refresh"></el-button>
					</template>
				</el-input>
			</div>
		</div>
		<div class="tabs-table-area">
			<my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
				@selectionChange="selectChange"
			>
        <!-- 大小 -->
				<template #size="{ row }">
					{{ capacityConversion(row.size) }}
				</template>
				<!-- 操作 -->
				<template #operation="{ row }">
				  <el-button type="danger" plain @click="deleteClick([row])">删除</el-button>
				</template>
			</my-table>
		</div>
    <TablePull ref="pullRef" @returnOK="returnOK" />
    <TableUpload ref="uploadRef" @returnOK="returnOK" />
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK" />
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { containerImageColumns } from '/@/model/vm'; // 表列、正则
import { capacityConversion } from '/@/model/resource'; // 表列、正则
import { containerQuery,containerDeleted } from '/@/api/ResourcePool/container'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TablePull = defineAsyncComponent(() => import('./TablePull.vue'));
const TableUpload = defineAsyncComponent(() => import('./TableUpload.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
	columns: containerImageColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	deleteTime: '',
});

interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = []
  if(true){
		let list = [
			{ id:'1', name: '容器jx1',size: 1 * (1**2),host: '*******' },
			{ id:'2', name: '容器jx2',size: 2 * (1024**1),host: '*******' },
			{ id:'3', name: '容器jx3',size: 3 * (1024**2),host: '*******' },
			{ id:'4', name: '容器jx4',size: 4 * (1024**3),host: '*******' },
			{ id:'5', name: '容器jx5',size: 5 * (1024**4),host: '*******' },
			{ id:'6', name: '容器jx6',size: 6 * (1024**5),host: '*******' },
		]
		return {
			data: list, // 数据
			total: list.length, // 总数
		};
  }
	return new Promise(async (resolve) => {
		containerQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页显示条数
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
const pullRef = ref(); // 拉取
const pullClick = () => {
	pullRef.value.openDialog(props.treeItem);
};
const uploadRef = ref(); // 上传
const uploadClick = () => {
	uploadRef.value.openDialog(props.treeItem);
};
const editRef = ref(); //  修改

// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '容器镜像/' + new Date();
	}
};
// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		containerDeleted({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res) => {
			if (res.msg == 'ok') {
				refresh();
				ElMessage.success('删除容器镜像操作完成');
			} else {
				ElMessage.error('删除容器镜像操作失败');
			}
		});
	} else {
		refresh();
	}
};
onMounted(() => {});
</script>
<style scoped lang="scss">
.resource-pool-container {
	width: calc(100%);
	height: calc(100%);
	.tabs-btn-area {
		height: 40px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		position: relative;
	}
}
</style>