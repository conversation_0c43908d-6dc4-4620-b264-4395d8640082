<template>
	<div class="dialog-tree-body">
		<div class="search-area">
			<el-input v-model="state.searchValue" class="search-input" placeholder="搜索节点" :prefix-icon="Search" @input="onSearch" />
		</div>
		<div class="tree-area">
			<el-tree
		  	ref="treeRef"
		  	:data="treeData"
		  	node-key="id"
        show-checkbox
		  	:props="defaultProps"
		  	:expand-on-click-node='false'
		  	default-expand-all
		  	:filter-node-method="filterNode"
		  	@check="handleCheckChange"
		  >
		  	<template #default="{ node, data }">
		  		<div class="custom-tree-node">
		  			<span class="node-label" :title="node.label">{{ node.label }}</span>
		  		</div>
		  	</template>
		  </el-tree>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import { FilterNodeMethodFunction, TreeInstance } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具
const props = defineProps({
	zNodes: {
		type: Array,
		required: true,
	},
});
const treeRef = ref<TreeInstance>()
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'name',
}
const state = reactive({
	treeItem: {},
	searchValue: '',
	lastSelectedNodeId: '1', // 保存上次选中的节点ID
});
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
const emit = defineEmits(['returnOK']);

// 初始化
const init = (data: any) => {
	treeData.value = convertFlatToTree(data, {
		idKey: 'id',
		pidKey: 'pid',
		childrenKey: 'children',
		rootValue: '0'
	});
  console.log('源数据',data);
  console.log('重构之后数据',treeData.value);
};

// 搜索
const onSearch = () => {
	treeRef.value!.filter(state.searchValue)
};

// 处理节点勾选变化
const handleCheckChange = (data: any, checked: boolean, indeterminate: boolean) => {
	// 获取所有选中的节点（包括半选状态的节点）
	const checkedNodes = treeRef.value!.getCheckedNodes();
	const halfCheckedNodes = treeRef.value!.getHalfCheckedNodes();
	// 合并选中和半选的节点
	const allSelectedNodes = [...checkedNodes, ...halfCheckedNodes];
	// 触发事件，将选中的节点传递给父组件
	emit('returnOK', allSelectedNodes);
	console.log('选中的节点:', checkedNodes);
	console.log('半选的节点:', halfCheckedNodes);
	console.log('所有相关节点:', allSelectedNodes);
};
watch(
	() => props.zNodes,
	(val) => {
    init(val)
	}
);
</script>
<style lang="scss" scoped>
.dialog-tree-body {
	width: 100%;
	height: 100%;
	padding: 10px;
	border: 1px solid #ccc;
	.search-area {
		height: 35px;
		display: flex;
		.search-input {
			margin-right: 10px;
		}
	}
	.tree-area {
		// width: 50%;
		width: 100%;
		height: calc(100% - 50px);
		overflow: auto;
    margin: 0 auto;
	}
}
</style>