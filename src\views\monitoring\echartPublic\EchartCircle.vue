<template>
	<div class="task-statistics">
		<my-echarts :options="state.chartOption"></my-echarts>
	</div>
</template>
<script setup lang="ts" name="EchartsCircle">
import { defineAsyncComponent, reactive, onMounted, ref, Ref, inject, watch } from 'vue';
const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));
import * as echarts from 'echarts';

const props = defineProps({
	storageRate: {
		type: Number,
		required: true,
	},
});
const getOption = () => {
	return {
		title: [
			{
				text: '使用量',
				x: 'center',
				top: '65%',
				textStyle: {
					fontSize: 16,
					fontWeight: '100',
				},
			},
			{
				text: props.storageRate + '%',
				x: 'center',
				top: '35%',
				textStyle: {
					fontSize: 25,
					fontFamily: 'DINAlternate-Bold, DINAlternate',
					foontWeight: '600',
				},
			},
		],
		// backgroundColor: '#111',
		polar: {
			radius: ['80%', '100%'],
			center: ['50%', '50%'],
		},
		angleAxis: {
			max: 100,
			show: false,
		},
		radiusAxis: {
			type: 'category',
			show: true,
			axisLabel: {
				show: false,
			},
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
		},
		series: [
			{
				name: '',
				type: 'bar',
				roundCap: true,
				barWidth: 90,
				showBackground: true,
				backgroundStyle: {
					color: 'rgba(66, 66, 66, .3)',
				},
				data: [props.storageRate],
				coordinateSystem: 'polar',
				itemStyle: {
					normal: {
						color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
							{
								offset: 0,
								color: '#6d3df1',
							},
							{
								offset: 1,
								color: '#b198f8',
							},
						]),
					},
				},
			},
		],
		// 下载图片
		// toolbox: {
		//   feature: {
		//     saveAsImage: {}
		//   }
		// },
	};
};
// 定义变量内容
const state = reactive<EmptyObjectType>({
	chartOption: getOption(),
});

// 页面加载时
onMounted(() => {
	window.addEventListener('resize', () => {
		state.chartOption = getOption();
	});
});
watch(
	() => props.storageRate,
	(val) => {
		state.chartOption = getOption();
	}
);
</script>
<style scoped lang="scss">
.task-statistics {
	height: calc(100%);
}
</style>
