<template>
	<el-form ref="formRef" label-position="left" :model="formMEM" :rules="rulesForm" label-width="150">
		<el-form-item prop="memory">
			<template #label>
				<div class="vm-new-label" @click="formMEM.typeShow = !formMEM.typeShow">
					<el-icon><ArrowDown v-show="formMEM.typeShow" /><ArrowRight v-show="!formMEM.typeShow" /></el-icon>
					<span>内存</span>
				</div>
			</template>
			<el-input v-model="formMEM.memory" type="number">
				<template #append>
					<el-select v-model="formMEM.unit" style="width: 80px">
						<el-option label="KB" value="KB" />
						<el-option label="MB" value="MB" />
						<el-option label="GB" value="GB" />
						<el-option label="TB" value="TB" />
					</el-select>
				</template>
			</el-input>
		</el-form-item>
		<div v-show="formMEM.typeShow">
			<el-form-item label="主机剩余内存">
				<el-input v-model="formMEM.surplusMem" :min="1" type="number" disabled>
					<template #append>GB</template>
				</el-input>
			</el-form-item>
			<el-form-item label="内存预留百分比">
				<el-input v-model="formMEM.reserveMem" :min="1" type="number" disabled>
					<template #append>%</template>
				</el-input>
			</el-form-item>
			<el-form-item label="内存分配策略">
				<el-select v-model="formMEM.strategy" style="width: 100%">
					<el-option label="请选择类型" value="none" />
					<el-option label="Strict" value="Strict" />
					<el-option label="Preferred" value="Preferred" />
					<el-option label="Interleave" value="Interleave" />
				</el-select>
			</el-form-item>
			<el-form-item label="绑定NUMA NODE">
				<el-input v-model="formMEM.bind" :min="1" disabled>
					<template #append>
						<el-button :icon="Search" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item></el-form-item>
		</div>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search,ArrowDown,ArrowRight } from '@element-plus/icons-vue'
import { propNumber } from '/@/model/resource'; // 表列、正则

const props = defineProps({
	memTime: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['memOK']);
// 硬件信息-内存
const formMEM = reactive({
	typeShow: false,
	memory: 2,
	unit: 'GB',
	surplusMem: 2,
	reserveMem: 1,
	strategy: 'none',
	bind: '',
});

const rulesForm = reactive<FormRules>({
	memory: [
    { required: true, message: '必填项' },
		{ validator: propNumber, trigger: 'bulk' }
	],
});
watch(
	() => props.memTime,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('memOK', formMEM);
				}else {
					emit('memOK', false)
				}
			});
		}
	}
);
</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>