<template>
	<div class="resource-pool-container">
		<div class="tabs-btn-area">
			<div>
				<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
				<el-button type="primary" plain @click="newClick" v-if="powerItem.tianjia">添加模板</el-button>
				<el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
			</div>
			<div v-if="powerItem.sousuo">
				<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
					<template #append>
						<el-button :icon="Search" @click="refresh"></el-button>
					</template>
				</el-input>
			</div>
		</div>
		<div class="tabs-table-area" v-if="powerItem.liebiao">
			<my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
				@selectionChange="selectChange"
			>
				<!-- 类型 -->
				<!-- <template #status="{ row }">
              <el-tag :type="row.status == 'enabled'?'success':'info'">{{row.status == 'enabled'?'启用':'禁用'}}</el-tag>
						</template> -->
				<!-- CPU架构 -->
				<template #cpu_arch="{ row }">
					<span>{{ frameworkConvert(row.cpu_arch) }}</span>
				</template>
				<!-- CPU数量 -->
				<template #vcpu="{ row }">
					<span>{{ row.vcpu + '核' }}</span>
				</template>
				<!-- 存储类型 -->
				<template #disk_type_code="{ row }">
					<span>{{ typeConvert(row.disk_type_code) }}</span>
				</template>
				<!-- 内存 -->
				<template #memory="{ row }">
					<span>{{ row.memory + row.memory_unit }}</span>
				</template>
				<!-- 操作 -->
				<template #operation="{ row }">
					<el-dropdown trigger="click" @command="commandItem($event, row)" v-if="powerItem.xiugai || powerItem.shanchu">
						<el-button type="primary"
							>操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon
						></el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
								<el-dropdown-item command="sc" style="color: red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<span v-else>-</span>
				</template>
			</my-table>
		</div>
		<TableOperate :newTime="state.newTime" :editTime="state.editTime" :tableRow="state.tableRow" :treeItem="props.treeItem" @returnOK="returnOK"></TableOperate>
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search,ArrowDownBold } from '@element-plus/icons-vue';
import { templateColumns, typeConvert, frameworkConvert } from '/@/model/templateManage'; // 表列、正则
import { templateQuery, templateDelete } from '/@/api/TemplateAPI'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableOperate = defineAsyncComponent(() => import('./TableOperate.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
	columns: templateColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	tableRow: {},
	newTime: '',
	editTime: '',
	deleteTime: '',
});

interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	return new Promise(async (resolve) => {
		templateQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页显示条数
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 表操作列
const commandItem = (item: string, row: any) => {
	state.tableRow = row;
	switch (item) {
		case 'bj':
			state.editTime = '' + new Date();
			break;
		case 'sc':
			deleteClick([row]);
			break;
	}
};
// 添加
const newClick = () => {
	state.newTime = '' + new Date();
};
// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '模板/' + new Date();
	}
};
// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		templateDelete({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res) => {
			if (res.msg == 'ok') {
				refresh();
				ElMessage.success('删除模板操作完成');
			} else {
				ElMessage.error('删除模板操作失败');
			}
		});
	} else {
		refresh();
	}
};
watch(
  ()=> props.treeItem,
  (val)=>{
    if(tableRef.value){
			refresh();
		}
  }
);
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'mobanliebiao',
    'mobansousuo',
    'tianjiamoban',
    'xiugaimoban',
    'shanchumoban',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.mobanliebiao;
		powerItem.sousuo = res.data.mobansousuo;
		powerItem.tianjia = res.data.tianjiamoban;
		powerItem.xiugai = res.data.xiugaimoban;
		powerItem.shanchu = res.data.shanchumoban;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style scoped lang="scss">
.resource-pool-container {
	width: calc(100%);
	height: calc(100%);
	.tabs-btn-area {
		height: 40px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		position: relative;
	}
}
</style>