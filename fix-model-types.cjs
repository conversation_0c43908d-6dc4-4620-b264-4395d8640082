const fs = require('fs');
const path = require('path');

// 递归查找所有 .ts 文件
function findTsFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findTsFiles(filePath));
      }
    } else {
      const ext = path.extname(file);
      if (ext === '.ts') {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的模型类型问题
function fixModelTypesInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 只处理 model 目录下的文件
    if (!filePath.includes('model')) {
      return false;
    }
    
    // 修复表格列定义的类型注解
    const columnPatterns = [
      // 匹配 const xxxColumns = [ 的模式
      /const\s+(\w*[Cc]olumns?)\s*=\s*\[/g,
      // 匹配 const xxxData = [ 的模式（可能是表格数据）
      /const\s+(\w*Data)\s*=\s*\[/g,
      // 匹配 const xxxSearch = [ 的模式（可能是搜索配置）
      /const\s+(\w*Search)\s*=\s*\[/g,
    ];
    
    columnPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        const varName = match[1];
        const fullMatch = match[0];
        
        // 检查是否已经有类型注解
        if (!fullMatch.includes(':')) {
          // 根据变量名判断应该使用什么类型
          let typeAnnotation = '';
          if (varName.toLowerCase().includes('column')) {
            typeAnnotation = ': MyTableColumns[]';
          } else if (varName.toLowerCase().includes('search')) {
            typeAnnotation = ': TableSearchType[]';
          } else if (varName.toLowerCase().includes('data') && content.includes('label') && content.includes('prop')) {
            // 如果包含 label 和 prop，很可能是表格列
            typeAnnotation = ': MyTableColumns[]';
          }
          
          if (typeAnnotation) {
            const newMatch = fullMatch.replace('=', typeAnnotation + ' =');
            content = content.replace(fullMatch, newMatch);
            modified = true;
          }
        }
      });
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed model types in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding TypeScript files to fix model types...');
  
  const files = findTsFiles(srcDir);
  console.log(`Found ${files.length} TypeScript files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixModelTypesInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed model types in ${fixedCount} files`);
}

main();
