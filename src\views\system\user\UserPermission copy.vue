<template>
	<el-dialog v-model="formItem.isShow" :title="formItem.titles" append-to-body class="dialog-500">
		<div class="permission-area">
			<ul id="treePermis" class="ztree"></ul>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="treeData">刷新</el-button>
				<el-button type="primary" @click="resetClick">重置权限</el-button>
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="UserPermission">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
import { powerTreeQuery,powerTreeEdit,powerTreeReset } from '/@/api/System'; // 接口
const formItem = reactive({
	titles: '用户权限',
	isShow: false,
	userName: '',
	userRole: '',
	userID: '',
  listZtree: null as any, // 明确指定类型为 any 以支持 zTree 对象
});
const setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
    key: {
      name: "module" // 这里是用来指定节点名称的字段
    }
	},
	view: {
		showIcon: false, // 显示图标
	},
  check: {
    enable: true // 启用复选框
  },
	callback: {},
};
interface Node {
	id: string;
	module: string;
	pid: string;
}
let zNodes: Node[] = [];
const treeData = () => {
	if (false) { // 修复：将 !true 改为 false，这样逻辑更清晰
		zNodes = [
			{ id: '1', module: '资源节点', pid: '0' },
			{ id: '2', module: '主机池1', pid: '1' },
			{ id: '3', module: '集群1', pid: '2' },
			{ id: '4', module: '主机1', pid: '3', },
			{ id: '5', module: '主机2', pid: '3', },
			{ id: '6', module: '主机3', pid: '3', },
			{ id: '7', module: '主机3', pid: '3', },
			{ id: '8', module: '主机3', pid: '3', },
			{ id: '9', module: '主机3', pid: '3', },
		];
		init()
	} else {
		powerTreeQuery({
      username: formItem.userName,
      role: formItem.userRole
    }).then((res) => {
			zNodes = res.data;
			init()
		}).catch((error) => {
			console.error('获取权限树数据失败:', error);
			ElMessage.error('获取权限数据失败');
		});
	}
};
const init = () => {
	let zTreeObj = window.$.fn.zTree.init($('#treePermis'), setting, zNodes); // 初始化树形控件
	zTreeObj.expandAll(true); // 默认展开所有树的分支
  formItem.listZtree = zTreeObj
};
// 重置权限
const resetClick = () => {
  ElMessageBox.confirm(
    `是否对 <span style="font-weight: 800">${formItem.userName}</span> 登录账号进行重置权限操作？`,
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    powerTreeReset({user_id: formItem.userID})
    .then(res=>{
      ElMessage.success(formItem.userName+'登录账号权限重置完成')
    })
  })
  .catch(() => {})
};
interface FilteredNode {
  id: any;
  pid: any;
  role: any;
  module: any;
  module_code: any;
  checked: any;
}

const getAllNodesRecursively=(nodes:any, allNodes: FilteredNode[] = []): FilteredNode[]=>{
  if (!nodes || nodes.length === 0) return allNodes;
  nodes.forEach((node:any) => {
    if (!node.checked) {
      const filteredNode: FilteredNode = {
        id: node.id,
        pid: node.pid,
        role: node.role,
        module: node.module,
        module_code: node.module_code,
        checked: node.checked,
      };
      allNodes.push(filteredNode);
    }
    if (node.children && node.children.length > 0) {
      getAllNodesRecursively(node.children, allNodes);
    }
  });
  return allNodes;
}
// 确认
const confirm = () => {
  if (!formItem.listZtree) {
    ElMessage.error('权限树未初始化');
    return;
  }
  let allNodes = getAllNodesRecursively(formItem.listZtree.getNodes())
  powerTreeEdit({
    permissions: allNodes,
    user_id: formItem.userID,
    user_role: formItem.userRole,
  }).then(res=>{
    ElMessage.success(formItem.userName+'账号权限设置完成')
    formItem.isShow = false
  }).catch((error) => {
    console.error('权限设置失败:', error);
    ElMessage.error('权限设置失败');
  });
};
// 打开弹窗
const openDialog = async (row: any) => {
	formItem.titles = row.username + '用户权限';
	formItem.userName = row.username
	formItem.userRole = row.role_name
	formItem.userID = row.id
	formItem.isShow = true;
	nextTick(() => {
    setTimeout(() => {
      treeData()
    }, 500);
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.permission-area {
	height: 400px;
	// overflow: auto;
}
</style>