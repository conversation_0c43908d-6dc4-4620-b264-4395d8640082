<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="system-content-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="refresh">刷新</el-button>
        </div>
        <div class="tabs-content-area">
          <el-card class="empower-card" v-for="item in state.empowerData" :key='item.name'>
            <h4>{{ item.name }}</h4>
            <h2>{{ item.value }}</h2>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts" name="Authorize">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
import { authorizeQuery } from '/@/api/System'; // 接口
const state = reactive({
  empowerData: [
    { name: '项目', value: '***' },
    { name: '节点数', value :'***' },
    { name: 'CPU', value :'***' },
    { name: '过期时间', value :'***' },
    { name: '集群码', value :'***' },
  ],
});
const getData = () => {
  authorizeQuery().then(res=>{
    let list = JSON.parse(res.data)
    let date = new Date(list.time);
    let year = date.getUTCFullYear();
    let month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
    let day = String(date.getUTCDate()).padStart(2, '0');
    let formattedDate = `${year}-${month}-${day}`
    state.empowerData = [
      { name: '项目', value: list.name },
      { name: '节点数', value: list.scale },
      { name: 'CPU', value: list.cpu },
      { name: '过期时间', value: formattedDate },
      { name: '集群码', value: list.code },
    ]
  })
}
const refresh = ()=>{
  getData()
}
onMounted(() => {
  refresh()
})
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .system-content-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
    }
    .tabs-content-area {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      align-content: space-around;
      justify-content: space-around;
      align-items: center;
      height: calc(100% - 50px);
      overflow: auto;
      .empower-card {
        width: 45%;
        height: 120px;
        margin-bottom: 10px;
        h2 {
          text-align: center;
          width: 100%;
          line-height: 50px;
          white-space: normal;
          overflow-wrap: break-word;
        }
      }
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>

