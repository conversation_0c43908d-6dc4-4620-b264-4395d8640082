$panel-width: 300px;
$panel-height: 400px;
$header-height: 40px;
$border-color: rgba(209, 216, 222, 1);
$header-bg-color: rgba(109, 119, 143, 1);
// 穿梭框
.tree-transfer {
  display: flex;
  align-items: center;

  .transfer-panel {
    width: $panel-width;
    height: $panel-height;
    border-radius: 5px;
    overflow: hidden;
    background: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);

    .panel-header {
      height: $header-height;
      line-height: $header-height;
      padding: 0 15px;
      background-color: $header-bg-color;
      border-bottom: 1px solid var(--el-border-color);
      border-radius: 5px 5px 0 0;
      color: rgba(255, 255, 255, 1);
    }

    .panel-body {
      padding: 10px;
      height: calc(100% - #{$header-height});
      overflow: auto;
      border: {
        left: 1px solid $border-color;
        right: 1px solid $border-color;
        bottom: 1px solid $border-color;
      }
    }
  }

  .transfer-buttons {
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    gap: 30px;

    .el-button {
      padding: 8px;
      height: auto;
      border-radius: 3px;
    }
    .el-icon{
      color: #fff;
    }
  }
  
  .custom-tree-node {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .disabled-label {
    color: var(--el-disabled-text-color);
    cursor: not-allowed;
  }

  .disabled-icon {
    color: var(--el-disabled-text-color);
  }

  :deep(.el-tree-node__content:hover) {
    color: var(--el-color-warning);
  }

  :deep(.el-tree-node.is-disabled .el-tree-node__content:hover) {
    color: var(--el-disabled-text-color);
    cursor: not-allowed;
    background-color: transparent;
  }

  :deep(.el-button+.el-button) {
    margin: 0 !important;
  }

  :deep(.is-disabled) {
    color: var(--el-disabled-text-color);
    cursor: not-allowed;
  }
}

// 树形控件
.el-tree{
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    .custom-tree-node-icon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 7px;
      .el-icon {
        width: 20px;
        height: 20px;
        cursor: pointer;
        color: #333;
        svg{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
