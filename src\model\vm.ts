// 添加虚拟机
// linux系统类型
const linuxData : MyTableColumns[] = [
	{ label: 'CentOS 6/7(32位)' },
	{ label: 'CentOS 6/7(64位)' },
	{ label: 'CentOS 8(64位)' },
	{ label: 'Debian GNU/Linux8(32位)' },
	{ label: 'Debian GNU/Linux8(64位)' },
	{ label: '深度操作系统(32位)' },
	{ label: '深度操作系统(64位)' },
	{ label: 'Fedora(32位)' },
	{ label: 'Fedora(64位)' },
	{ label: 'SUSE Linux Enterprise11(32位)' },
	{ label: 'SUSE Linux Enterprise11(64位)' },
	{ label: 'SUSE Linux Enterprise12(64位)' },
	{ label: 'Oracle Linux6(32位)' },
	{ label: 'Oracle Linux6(64位)' },
	{ label: 'Oracle Linux7(32位)' },
	{ label: 'Oracle Linux7(64位)' },
	{ label: 'Asianux Server2(32位)' },
	{ label: 'Asianux Server2(64位)' },
	{ label: 'Asianux Server3(32位)' },
	{ label: 'Asianux Server3(64位)' },
	{ label: 'Asianux Server4(32位)' },
	{ label: 'Asianux Server4(64位)' },
	{ label: 'Red Hat Enterprise Linux6(32位)' },
	{ label: 'Red Hat Enterprise Linux6(64位)' },
	{ label: 'Red Hat Enterprise Linux7(32位)' },
	{ label: 'Red Hat Enterprise Linux7(64位)' },
	{ label: 'Red Hat Enterprise Linux8(64位)' },
	{ label: 'openSUSE(32位)' },
	{ label: 'openSUSE(64位)' },
	{ label: 'Ubuntu Server(32位)' },
	{ label: 'Ubuntu Server(64位)' },
	{ label: '凝思磐石安全操作系统(32位)' },
	{ label: '凝思磐石安全操作系统(64位)' },
	{ label: '普华服务器版操作系统V3.0(64位)' },
	{ label: '普华服务器版操作系统V4.0(64位)' },
	{ label: '一铭服务器操作系统V1.0(32位)' },
	{ label: '一铭服务器操作系统V1.0(64位)' },
	{ label: '中标普华桌面版V4(32位)' },
	{ label: '中兴新支点NewStart CGSL(64位)' },
	{ label: '中标麒麟服务器版V6(32位)' },
	{ label: '中标麒麟服务器版V6(64位)' },
	{ label: '中标麒麟高级服务器版V7(64位)' },
	{ label: '中标麒麟高级服务器版V10(64位)' },
	{ label: '中标麒麟桌面版V5(32位)' },
	{ label: '麒麟信安64(64位)' },
	{ label: '银河麒麟高级服务器版V4(64位)' },
	{ label: '华为EulerOS(64位)' },
	{ label: '统信UOS V20(64位)' },
	{ label: '中科方德(64位)' },
	{ label: 'Linux(Hygon版)' },
	{ label: 'openEuler(64位)' },
	{ label: '其它 Linux(32位)' },
	{ label: '其它 Linux(64位)' },
];
// Windows系统类型
const windowsData : MyTableColumns[] = [
	{ label: 'Microsoft Windows 10(32位)' },
	{ label: 'Microsoft Windows 10(64位)' },
	{ label: 'Microsoft Windows 8(32位)' },
	{ label: 'Microsoft Windows 8(64位)' },
	{ label: 'Microsoft Windows 8.1(32位)' },
	{ label: 'Microsoft Windows 8.1(64位)' },
	{ label: 'Microsoft Windows 7(32位)' },
	{ label: 'Microsoft Windows 7(64位)' },
	{ label: 'Microsoft Windows Server 2003 R2(32位)' },
	{ label: 'Microsoft Windows Server 2003(32位)' },
	{ label: 'Microsoft Windows Server 2003(64位)' },
	{ label: 'Microsoft Windows Server 2008 R2(64位)' },
	{ label: 'Microsoft Windows Server 2008 R2(32位)' },
	{ label: 'Microsoft Windows Server 2008(32位)' },
	{ label: 'Microsoft Windows Server 2008(64位)' },
	{ label: 'Microsoft Windows Server 2012 R2(64位)' },
	{ label: 'Microsoft Windows Server 2012(32位)' },
	{ label: 'Microsoft Windows Server 2012(64位)' },
	{ label: 'Microsoft Windows Server 2016(64位)' },
	{ label: 'Microsoft Windows Server 2019(64位)' },
	{ label: 'Microsoft Windows Vista(32位)' },
	{ label: 'Microsoft Windows Vista(64位)' },
	{ label: 'Microsoft Windows XP Professional(32位)' },
	{ label: 'Microsoft Windows XP Professional(64位)' },
	{ label: '其它Microsoft Windows(32位)' },
	{ label: '其它Microsoft Windows(64位)' },
	{ label: 'Windows(Hygon版)' },
];
// 其余系统类型
const qitaData : MyTableColumns[] = [{ label: '其它系统64' }, { label: '其它系统32' }];

const driveData : MyTableColumns[] = [
	{ label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '镜像名称', prop: 'name', sortable: true, align: 'center' },
	{ label: '镜像类型', prop: 'type', align: 'center' },
];

// 容器
const containerColumns : MyTableColumns[] = [
	{ type: 'selection', wrap: true },
	{ label: '容器名称', prop: 'name', sortable: true },
	{ label: '镜像', prop: 'image', sortable: true, align: 'center' },
	{ label: 'CPU', prop: 'cpu', align: 'center' },
	{ label: '内存', tdSlot: 'memory', align: 'center' },
	{ label: 'IP地址', tdSlot: 'ip', align: 'center' },
	{ label: '状态', tdSlot: 'status', align: 'center' },
	{ label: '任务状态', tdSlot: 'task_state', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: 120, wrap: true },
];
// 容器镜像
const containerImageColumns : MyTableColumns[] = [
	{ type: 'selection', wrap: true },
	{ label: '容器镜像名称', prop: 'name', sortable: true },
	{ label: '大小', tdSlot: 'size', align: 'center' },
	{ label: '主机', prop: 'host', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: 120, wrap: true },
];
// 状态
const statusConvert = (row: string) => {
	switch (row) {
		case 'Creating':
			return '创建中';
		case 'Created':
			return '已创建';
		case 'Running':
			return '运行中';
		case 'Stopped':
			return '停止';
		case 'Restarting':
			return '重启中';
		case 'Paused':
			return '暂停';
		case 'Error':
			return '错误';
		default:
			return '未知';
	}
};

export { linuxData, windowsData, qitaData, driveData, containerColumns, containerImageColumns, statusConvert };
