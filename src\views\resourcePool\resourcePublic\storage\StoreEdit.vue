<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="修改存储池"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="存储池名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入存储池名称"/>
      </el-form-item>
      
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { storagePollEdit } from '/@/api/ResourcePool/storage'; // 接口
import { propName } from '/@/model/resource'; // 表列、正则

const props = defineProps({
  tableRow: {
    type: Object,
    required: true
  },
  editTime: {
    type: String,
    required: true
  }
});
// const propName = (rule: any, value: any, callback: any) => {
// 	const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
// 	if (!regex.test(value)) {
// 		callback(new Error('2-32 个中文、英文、数字、特殊字符@_.-'));
// 	} else {
//     if(props.tableRow.name != value){
// 		  callback();
//     }else {
//       callback(new Error('新名称不能和旧名称相同'));
//     }
// 	}
// };
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
});
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" },
  ],
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        storagePollEdit({
          name: props.tableRow.name,
          new_name: formItem.name,
          id: props.tableRow.id
        })
        .then((res:any) => {
          if(res.msg == 'ok') {
            ElMessage.success('修改存储池操作完成');
            emit('returnOK', 'refresh');
          }else {
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}
watch(
  ()=> props.editTime,
  (val)=>{
    formItem.isShow = true;
    formItem.name = props.tableRow.name
  }
);
</script>