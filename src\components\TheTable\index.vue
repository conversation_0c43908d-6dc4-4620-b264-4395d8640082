<template>
  <el-table
    :data="data"
    :height="height"
    :max-height="maxHeight"
    :stripe="stripe"
    :border="border"
    :size="size"
    :fit="fit"
    :show-header="showHeader"
    :highlight-current-row="highlightCurrentRow"
    :row-class-name="rowClassName"
    :row-style="rowStyle"
    :cell-class-name="cellClassName"
    :cell-style="cellStyle"
    :header-row-class-name="headerRowClassName"
    :header-row-style="headerRowStyle"
    :header-cell-class-name="headerCellClassName"
    :header-cell-style="headerCellStyle"
    :row-key="rowKey"
    :empty-text="emptyText"
    :default-expand-all="defaultExpandAll"
    :tree-props="treeProps"
    :summary-method="summaryMethod"
    :sum-text="sumText"
    :index="index"
    :show-summary="showSummary"
    :span-method="spanMethod"
    :select-on-indeterminate="selectOnIndeterminate"
    :indent="indent"
    :lazy="lazy"
    :load="load"
    :table-layout="tableLayout"
    :show-overflow-tooltip="showOverflowTooltip"
    v-bind="$attrs"
    @select="$emit('select', $event)"
    @select-all="$emit('select-all', $event)"
    @selection-change="$emit('selection-change', $event)"
    @cell-mouse-enter="$emit('cell-mouse-enter', $event)"
    @cell-mouse-leave="$emit('cell-mouse-leave', $event)"
    @cell-click="$emit('cell-click', $event)"
    @cell-dblclick="$emit('cell-dblclick', $event)"
    @cell-contextmenu="$emit('cell-contextmenu', $event)"
    @row-click="$emit('row-click', $event)"
    @row-contextmenu="$emit('row-contextmenu', $event)"
    @row-dblclick="$emit('row-dblclick', $event)"
    @header-click="$emit('header-click', $event)"
    @header-contextmenu="$emit('header-contextmenu', $event)"
    @sort-change="$emit('sort-change', $event)"
    @filter-change="$emit('filter-change', $event)"
    @current-change="$emit('current-change', $event)"
    @expand-change="$emit('expand-change', $event)"
  >
    <!-- 动态插槽处理 -->
    <slot v-if="slots.default" />
    
    <!-- 如果没有提供插槽，则根据columns属性自动生成列 -->
    <template v-else>
      <!-- 渲染选择列 -->
      <el-table-column v-if="selection" type="selection" :width="selectionWidth" />
      
      <!-- 渲染自定义列 -->
      <el-table-column
        v-for="(column, index) in columns"
        :key="column.prop || index"
        :type="column.type"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :show-overflow-tooltip="column.showOverflowTooltip ?? showOverflowTooltip"
        :sortable="column.sortable"
        :sort-method="column.sortMethod"
        :sort-by="column.sortBy"
        :formatter="column.formatter"
        :filters="column.filters"
        :filter-method="column.filterMethod"
        :filter-multiple="column.filterMultiple"
        :align="column.align || align"
        :header-align="column.headerAlign || headerAlign"
        :class-name="column.className"
        :label-class-name="column.labelClassName"
      >
        <template v-if="column.slot" #default="scope">
          <slot :name="column.slot" v-bind="scope" />
        </template>
        <template v-else-if="column.render" #default="scope">
          <component :is="column.render" v-bind="scope" />
        </template>
        <template v-if="column.headerSlot" #header="scope">
          <slot :name="column.headerSlot" v-bind="scope" />
        </template>
      </el-table-column>
    </template>
    
    <!-- 自定义空状态 -->
    <template #empty>
      <div class="table-empty-wrapper">
        <img src="../../assets/images/noData.svg" alt="无数据" class="empty-image" :class="emptySize" />
      </div>
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { useSlots } from 'vue'

const slots = useSlots()

defineOptions({
  name: 'TheTable',
  inheritAttrs: false
})

// 定义插槽类型
interface TableSlots {
  // 默认插槽
  default?: () => any
  // 空状态插槽
  empty?: () => any
  // 动态插槽 - 支持任意名称的插槽，通常用于列的自定义渲染
  [key: string]: ((props: { 
    row: any, 
    column: any, 
    $index: number 
  }) => any) | (() => any) | undefined
}

// 定义表格作用域插槽的类型
declare module 'vue' {
  interface ComponentCustomProperties {
    $slots: TableSlots
  }
}

// 定义插槽类型
defineSlots<TableSlots>()

// 定义列的类型
interface TableColumn {
  prop?: string;
  label?: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | 'left' | 'right';
  type?: string;
  showOverflowTooltip?: boolean;
  sortable?: boolean;
  sortMethod?: Function;
  sortBy?: string | Function;
  formatter?: Function;
  filters?: Array<{ text: string, value: any }>;
  filterMethod?: Function;
  filterMultiple?: boolean;
  align?: 'left' | 'center' | 'right';
  headerAlign?: 'left' | 'center' | 'right';
  className?: string;
  labelClassName?: string;
  slot?: string;
  headerSlot?: string;
  render?: any;
}

defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  columns: {
    type: Array as () => TableColumn[],
    default: () => []
  },
  // 表格高度
  height: [String, Number],
  // 表格最大高度
  maxHeight: [String, Number],
  // 是否为斑马纹表格
  stripe: {
    type: Boolean,
    default: false
  },
  // 是否带有纵向边框
  border: {
    type: Boolean,
    default: false
  },
  // Table 的尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 空图片的尺寸
  emptySize: {
    type: String,
		default: 'default',
  },
  // 列的宽度是否自撑开
  fit: {
    type: Boolean,
    default: true
  },
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否要高亮当前行
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  // 行数据的Key
  rowKey: [String, Function],
  // 默认展开所有行
  defaultExpandAll: {
    type: Boolean,
    default: false
  },
  // 树形数据配置
  treeProps: {
    type: Object,
    default: () => ({ children: 'children', hasChildren: 'hasChildren' })
  },
  // 自定义的合计计算方法
  summaryMethod: Function,
  // 表格合计行第一列的文本
  sumText: {
    type: String,
    default: '合计'
  },
  // 自定义索引方法
  index: Function,
  // 是否在表尾显示合计行
  showSummary: {
    type: Boolean,
    default: false
  },
  // 合并行或列的计算方法
  spanMethod: Function,
  // 在多选时，是否把选中状态传递给父级或子级
  selectOnIndeterminate: {
    type: Boolean,
    default: true
  },
  // 树形数据的缩进
  indent: {
    type: Number,
    default: 16
  },
  // 是否懒加载子节点数据
  lazy: {
    type: Boolean,
    default: false
  },
  // 加载子节点数据的函数
  load: Function,
  // 表格布局方式
  tableLayout: {
    type: String,
    default: 'fixed'
  },
  // 当内容过长时是否显示省略号
  showOverflowTooltip: {
    type: Boolean,
    default: false
  },
  // 对齐方式
  align: {
    type: String,
    default: 'left'
  },
  // 表头对齐方式
  headerAlign: {
    type: String,
    default: ''
  },
  // 行的 className
  rowClassName: [String, Function],
  // 行的 style
  rowStyle: [Object, Function],
  // 单元格的 className
  cellClassName: [String, Function],
  // 单元格的 style
  cellStyle: [Object, Function],
  // 表头行的 className
  headerRowClassName: [String, Function],
  // 表头行的 style
  headerRowStyle: [Object, Function],
  // 表头单元格的 className
  headerCellClassName: [String, Function],
  // 表头单元格的 style
  headerCellStyle: [Object, Function],
  // 是否显示选择列
  selection: {
    type: Boolean,
    default: false
  },
  // 选择列宽度
  selectionWidth: {
    type: [String, Number],
    default: 55
  },
  // 空数据文本
  emptyText: {
    type: String,
    default: '暂无数据'
  }
})

defineEmits([
  'select',
  'select-all',
  'selection-change',
  'cell-mouse-enter',
  'cell-mouse-leave',
  'cell-click',
  'cell-dblclick',
  'cell-contextmenu',
  'row-click',
  'row-contextmenu',
  'row-dblclick',
  'header-click',
  'header-contextmenu',
  'sort-change',
  'filter-change',
  'current-change',
  'expand-change'
])
</script>

<style scoped lang="scss">
/* 可根据需要添加样式 */
.table-empty-wrapper {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  height: auto;
  margin-bottom: 15px;
  &.default {
    width: 200px;
  }
  &.small {
    width: 100px;
  }
}
</style> 