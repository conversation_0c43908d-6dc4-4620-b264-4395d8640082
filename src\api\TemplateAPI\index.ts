import request from '/@/utils/request';
// 模板 查询
export const templateQuery = (data: object) => {
	return request({
		url: '/theapi/v5/template/list',
		method: 'post',
		data,
	});
};
// 模板 添加
export const templateNew = (data: object) => {
	return request({
		url: '/theapi/v5/template/add',
		method: 'post',
		data,
	});
};
// 模板修改
export const templateEdit = (data: object) => {
	return request({
		url: '/theapi/v5/template/put',
		method: 'put',
		data,
	});
};
// 模板删除
export const templateDelete = (data: object) => {
	return request({
		url: '/theapi/v5/template/delete',
		method: 'delete',
		data,
	});
};
