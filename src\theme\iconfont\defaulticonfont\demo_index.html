<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2298093" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xeb80;</span>
                <div class="name">全局设置_o</div>
                <div class="code-name">&amp;#xeb80;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebb3;</span>
                <div class="name">云上传_o</div>
                <div class="code-name">&amp;#xebb3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebb4;</span>
                <div class="name">云下载_o</div>
                <div class="code-name">&amp;#xebb4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">电脑</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">二维码</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">终端参数查询</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">首页_动态灰</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">普通</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">动态</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">温度参数</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">终端参数</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">电 话</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">显示密码</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">隐藏密码</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67a;</span>
                <div class="name">树型</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">树型</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe685;</span>
                <div class="name">树型图</div>
                <div class="code-name">&amp;#xe685;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe745;</span>
                <div class="name">波浪能</div>
                <div class="code-name">&amp;#xe745;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe746;</span>
                <div class="name">波浪能试验场</div>
                <div class="code-name">&amp;#xe746;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d1;</span>
                <div class="name">27-拆分行</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d0;</span>
                <div class="name">26-拆分列</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67e;</span>
                <div class="name">图片预览</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">15.图片预览</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">728编辑器_字体大小</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">字体</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaef;</span>
                <div class="name">字体大小</div>
                <div class="code-name">&amp;#xeaef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a8;</span>
                <div class="name">拖动</div>
                <div class="code-name">&amp;#xe6a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">中英文</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe714;</span>
                <div class="name">符号-英文</div>
                <div class="code-name">&amp;#xe714;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">符号-中文</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">地球</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">星球</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">地球</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">环境 星球</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">自定义布局</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">步骤</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">30_选中圆形_fill</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">失败</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">7_round_solid_数字7_by_climei</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">6_round_solid_数字6_by_climei</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">9_round_solid_数字9_by_climei</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">1_round_solid_数字1_by_climei</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">5_round_solid_数字5_by_climei</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">2_round_solid_数字2_by_climei</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">0_round_solid_数字0_by_climei</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">3_round_solid_数字3_by_climei</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">4_round_solid_数字4_by_climei</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">8_round_solid_数字8_by_climei</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea6b;</span>
                <div class="name">圆形</div>
                <div class="code-name">&amp;#xea6b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">二维码</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76a;</span>
                <div class="name">查找 表单 列表</div>
                <div class="code-name">&amp;#xe76a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">表单</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">思维导图</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">级联动选择器</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">裁剪</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">富文本</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">富文本框</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">选择器</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">方框</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe77b;</span>
                <div class="name">79勾选-未选中-线性方框</div>
                <div class="code-name">&amp;#xe77b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">湿度</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67d;</span>
                <div class="name">烟感</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe686;</span>
                <div class="name">温度</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">噪声</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">今日待办</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">13 AI 实验室</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">申请开班</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">中英文切换</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">中英文</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a2;</span>
                <div class="name">中英转换</div>
                <div class="code-name">&amp;#xe6a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">数据4</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ff;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe772;</span>
                <div class="name">复制 页面</div>
                <div class="code-name">&amp;#xe772;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">操作-外链</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">个人中心</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">系统设置</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">内嵌数据储存</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">权限</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe85e;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe85e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">功能</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">工具</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">皮肤</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">实心圆</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a9;</span>
                <div class="name">点选</div>
                <div class="code-name">&amp;#xe6a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">点</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">退出全屏</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1678676993800') format('woff2'),
       url('iconfont.woff?t=1678676993800') format('woff'),
       url('iconfont.ttf?t=1678676993800') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-quanjushezhi_o"></span>
            <div class="name">
              全局设置_o
            </div>
            <div class="code-name">.icon-quanjushezhi_o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunshangchuan_o"></span>
            <div class="name">
              云上传_o
            </div>
            <div class="code-name">.icon-yunshangchuan_o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunxiazai_o"></span>
            <div class="name">
              云下载_o
            </div>
            <div class="code-name">.icon-yunxiazai_o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diannao1"></span>
            <div class="name">
              电脑
            </div>
            <div class="code-name">.icon-diannao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-barcode-qr"></span>
            <div class="name">
              二维码
            </div>
            <div class="code-name">.icon-barcode-qr
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongduancanshuchaxun"></span>
            <div class="name">
              终端参数查询
            </div>
            <div class="code-name">.icon-zhongduancanshuchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye_dongtaihui"></span>
            <div class="name">
              首页_动态灰
            </div>
            <div class="code-name">.icon-shouye_dongtaihui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-putong"></span>
            <div class="name">
              普通
            </div>
            <div class="code-name">.icon-putong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dongtai"></span>
            <div class="name">
              动态
            </div>
            <div class="code-name">.icon-dongtai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenducanshu-05"></span>
            <div class="name">
              温度参数
            </div>
            <div class="code-name">.icon-wenducanshu-05
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongduancanshu"></span>
            <div class="name">
              终端参数
            </div>
            <div class="code-name">.icon-zhongduancanshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi1"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.icon-tongzhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi2"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.icon-tongzhi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi3"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.icon-tongzhi3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi4"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.icon-tongzhi4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianhua"></span>
            <div class="name">
              电 话
            </div>
            <div class="code-name">.icon-dianhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xianshimima"></span>
            <div class="name">
              显示密码
            </div>
            <div class="code-name">.icon-xianshimima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yincangmima"></span>
            <div class="name">
              隐藏密码
            </div>
            <div class="code-name">.icon-yincangmima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuxing"></span>
            <div class="name">
              树型
            </div>
            <div class="code-name">.icon-shuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juxingkaobei"></span>
            <div class="name">
              树型
            </div>
            <div class="code-name">.icon-juxingkaobei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuxingtu"></span>
            <div class="name">
              树型图
            </div>
            <div class="code-name">.icon-shuxingtu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bolangneng"></span>
            <div class="name">
              波浪能
            </div>
            <div class="code-name">.icon-bolangneng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bolangnengshiyanchang"></span>
            <div class="name">
              波浪能试验场
            </div>
            <div class="code-name">.icon-bolangnengshiyanchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon--chaifenhang"></span>
            <div class="name">
              27-拆分行
            </div>
            <div class="code-name">.icon--chaifenhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon--chaifenlie"></span>
            <div class="name">
              26-拆分列
            </div>
            <div class="code-name">.icon--chaifenlie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupianyulan"></span>
            <div class="name">
              图片预览
            </div>
            <div class="code-name">.icon-tupianyulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-15tupianyulan"></span>
            <div class="name">
              15.图片预览
            </div>
            <div class="code-name">.icon-15tupianyulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-728bianjiqi_zitidaxiao"></span>
            <div class="name">
              728编辑器_字体大小
            </div>
            <div class="code-name">.icon-728bianjiqi_zitidaxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziti"></span>
            <div class="name">
              字体
            </div>
            <div class="code-name">.icon-ziti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-font-size"></span>
            <div class="name">
              字体大小
            </div>
            <div class="code-name">.icon-font-size
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuodong"></span>
            <div class="name">
              拖动
            </div>
            <div class="code-name">.icon-tuodong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongyingwen1"></span>
            <div class="name">
              中英文
            </div>
            <div class="code-name">.icon-zhongyingwen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuhao-yingwen"></span>
            <div class="name">
              符号-英文
            </div>
            <div class="code-name">.icon-fuhao-yingwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuhao-zhongwen"></span>
            <div class="name">
              符号-中文
            </div>
            <div class="code-name">.icon-fuhao-zhongwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diqiu"></span>
            <div class="name">
              地球
            </div>
            <div class="code-name">.icon-diqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingqiu"></span>
            <div class="name">
              星球
            </div>
            <div class="code-name">.icon-xingqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diqiu1"></span>
            <div class="name">
              地球
            </div>
            <div class="code-name">.icon-diqiu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huanjingxingqiu"></span>
            <div class="name">
              环境 星球
            </div>
            <div class="code-name">.icon-huanjingxingqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidingyibuju"></span>
            <div class="name">
              自定义布局
            </div>
            <div class="code-name">.icon-zidingyibuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayin"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.icon-dayin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-step"></span>
            <div class="name">
              步骤
            </div>
            <div class="code-name">.icon-step
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-30xuanzhongyuanxingfill"></span>
            <div class="name">
              30_选中圆形_fill
            </div>
            <div class="code-name">.icon-30xuanzhongyuanxingfill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shibai"></span>
            <div class="name">
              失败
            </div>
            <div class="code-name">.icon-shibai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-7_round_solid"></span>
            <div class="name">
              7_round_solid_数字7_by_climei
            </div>
            <div class="code-name">.icon-7_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-6_round_solid"></span>
            <div class="name">
              6_round_solid_数字6_by_climei
            </div>
            <div class="code-name">.icon-6_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-9_round_solid"></span>
            <div class="name">
              9_round_solid_数字9_by_climei
            </div>
            <div class="code-name">.icon-9_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-1_round_solid"></span>
            <div class="name">
              1_round_solid_数字1_by_climei
            </div>
            <div class="code-name">.icon-1_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-5_round_solid"></span>
            <div class="name">
              5_round_solid_数字5_by_climei
            </div>
            <div class="code-name">.icon-5_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-2_round_solid"></span>
            <div class="name">
              2_round_solid_数字2_by_climei
            </div>
            <div class="code-name">.icon-2_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-0_round_solid"></span>
            <div class="name">
              0_round_solid_数字0_by_climei
            </div>
            <div class="code-name">.icon-0_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-3_round_solid"></span>
            <div class="name">
              3_round_solid_数字3_by_climei
            </div>
            <div class="code-name">.icon-3_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-4_round_solid"></span>
            <div class="name">
              4_round_solid_数字4_by_climei
            </div>
            <div class="code-name">.icon-4_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-8_round_solid"></span>
            <div class="name">
              8_round_solid_数字8_by_climei
            </div>
            <div class="code-name">.icon-8_round_solid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-radio-off-full"></span>
            <div class="name">
              圆形
            </div>
            <div class="code-name">.icon-radio-off-full
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.icon-tongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ico"></span>
            <div class="name">
              二维码
            </div>
            <div class="code-name">.icon-ico
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chazhaobiaodanliebiao"></span>
            <div class="name">
              查找 表单 列表
            </div>
            <div class="code-name">.icon-chazhaobiaodanliebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaodan"></span>
            <div class="name">
              表单
            </div>
            <div class="code-name">.icon-biaodan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-siweidaotu"></span>
            <div class="name">
              思维导图
            </div>
            <div class="code-name">.icon-siweidaotu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiliandongxuanzeqi"></span>
            <div class="name">
              级联动选择器
            </div>
            <div class="code-name">.icon-jiliandongxuanzeqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caijian"></span>
            <div class="name">
              裁剪
            </div>
            <div class="code-name">.icon-caijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuwenben"></span>
            <div class="name">
              富文本
            </div>
            <div class="code-name">.icon-fuwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuwenbenkuang"></span>
            <div class="name">
              富文本框
            </div>
            <div class="code-name">.icon-fuwenbenkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangchuan"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.icon-shangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanzeqi"></span>
            <div class="name">
              选择器
            </div>
            <div class="code-name">.icon-xuanzeqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fangkuang"></span>
            <div class="name">
              方框
            </div>
            <div class="code-name">.icon-fangkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gouxuan-weixuanzhong-xianxingfangkuang"></span>
            <div class="name">
              79勾选-未选中-线性方框
            </div>
            <div class="code-name">.icon-gouxuan-weixuanzhong-xianxingfangkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shidu"></span>
            <div class="name">
              湿度
            </div>
            <div class="code-name">.icon-shidu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yangan"></span>
            <div class="name">
              烟感
            </div>
            <div class="code-name">.icon-yangan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendu"></span>
            <div class="name">
              温度
            </div>
            <div class="code-name">.icon-wendu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zaosheng"></span>
            <div class="name">
              噪声
            </div>
            <div class="code-name">.icon-zaosheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jinridaiban"></span>
            <div class="name">
              今日待办
            </div>
            <div class="code-name">.icon-jinridaiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-AIshiyanshi"></span>
            <div class="name">
              13 AI 实验室
            </div>
            <div class="code-name">.icon-AIshiyanshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingkaiban"></span>
            <div class="name">
              申请开班
            </div>
            <div class="code-name">.icon-shenqingkaiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongyingwenqiehuan"></span>
            <div class="name">
              中英文切换
            </div>
            <div class="code-name">.icon-zhongyingwenqiehuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongyingwen"></span>
            <div class="name">
              中英文
            </div>
            <div class="code-name">.icon-zhongyingwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongyingzhuanhuan"></span>
            <div class="name">
              中英转换
            </div>
            <div class="code-name">.icon-zhongyingzhuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuju"></span>
            <div class="name">
              数据4
            </div>
            <div class="code-name">.icon-shuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ico_shuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.icon-ico_shuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuju1"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.icon-shuju1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhiyemian"></span>
            <div class="name">
              复制 页面
            </div>
            <div class="code-name">.icon-fuzhiyemian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caozuo-wailian"></span>
            <div class="name">
              操作-外链
            </div>
            <div class="code-name">.icon-caozuo-wailian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.icon-icon-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin"></span>
            <div class="name">
              个人中心
            </div>
            <div class="code-name">.icon-gerenzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xitongshezhi"></span>
            <div class="name">
              系统设置
            </div>
            <div class="code-name">.icon-xitongshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-neiqianshujuchucun"></span>
            <div class="name">
              内嵌数据储存
            </div>
            <div class="code-name">.icon-neiqianshujuchucun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanxian"></span>
            <div class="name">
              权限
            </div>
            <div class="code-name">.icon-quanxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zujian"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.icon-zujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-crew_feature"></span>
            <div class="name">
              功能
            </div>
            <div class="code-name">.icon-crew_feature
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongju"></span>
            <div class="name">
              工具
            </div>
            <div class="code-name">.icon-gongju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-skin"></span>
            <div class="name">
              皮肤
            </div>
            <div class="code-name">.icon-skin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shixinyuan"></span>
            <div class="name">
              实心圆
            </div>
            <div class="code-name">.icon-shixinyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-webicon318"></span>
            <div class="name">
              点选
            </div>
            <div class="code-name">.icon-webicon318
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dian"></span>
            <div class="name">
              点
            </div>
            <div class="code-name">.icon-dian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fullscreen"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-fullscreen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuichuquanping"></span>
            <div class="name">
              退出全屏
            </div>
            <div class="code-name">.icon-tuichuquanping
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanjushezhi_o"></use>
                </svg>
                <div class="name">全局设置_o</div>
                <div class="code-name">#icon-quanjushezhi_o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunshangchuan_o"></use>
                </svg>
                <div class="name">云上传_o</div>
                <div class="code-name">#icon-yunshangchuan_o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunxiazai_o"></use>
                </svg>
                <div class="name">云下载_o</div>
                <div class="code-name">#icon-yunxiazai_o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diannao1"></use>
                </svg>
                <div class="name">电脑</div>
                <div class="code-name">#icon-diannao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-barcode-qr"></use>
                </svg>
                <div class="name">二维码</div>
                <div class="code-name">#icon-barcode-qr</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongduancanshuchaxun"></use>
                </svg>
                <div class="name">终端参数查询</div>
                <div class="code-name">#icon-zhongduancanshuchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye_dongtaihui"></use>
                </svg>
                <div class="name">首页_动态灰</div>
                <div class="code-name">#icon-shouye_dongtaihui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-putong"></use>
                </svg>
                <div class="name">普通</div>
                <div class="code-name">#icon-putong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dongtai"></use>
                </svg>
                <div class="name">动态</div>
                <div class="code-name">#icon-dongtai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenducanshu-05"></use>
                </svg>
                <div class="name">温度参数</div>
                <div class="code-name">#icon-wenducanshu-05</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongduancanshu"></use>
                </svg>
                <div class="name">终端参数</div>
                <div class="code-name">#icon-zhongduancanshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi1"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#icon-tongzhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi2"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#icon-tongzhi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi3"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#icon-tongzhi3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi4"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#icon-tongzhi4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianhua"></use>
                </svg>
                <div class="name">电 话</div>
                <div class="code-name">#icon-dianhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xianshimima"></use>
                </svg>
                <div class="name">显示密码</div>
                <div class="code-name">#icon-xianshimima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yincangmima"></use>
                </svg>
                <div class="name">隐藏密码</div>
                <div class="code-name">#icon-yincangmima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuxing"></use>
                </svg>
                <div class="name">树型</div>
                <div class="code-name">#icon-shuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juxingkaobei"></use>
                </svg>
                <div class="name">树型</div>
                <div class="code-name">#icon-juxingkaobei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuxingtu"></use>
                </svg>
                <div class="name">树型图</div>
                <div class="code-name">#icon-shuxingtu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bolangneng"></use>
                </svg>
                <div class="name">波浪能</div>
                <div class="code-name">#icon-bolangneng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bolangnengshiyanchang"></use>
                </svg>
                <div class="name">波浪能试验场</div>
                <div class="code-name">#icon-bolangnengshiyanchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon--chaifenhang"></use>
                </svg>
                <div class="name">27-拆分行</div>
                <div class="code-name">#icon--chaifenhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon--chaifenlie"></use>
                </svg>
                <div class="name">26-拆分列</div>
                <div class="code-name">#icon--chaifenlie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupianyulan"></use>
                </svg>
                <div class="name">图片预览</div>
                <div class="code-name">#icon-tupianyulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-15tupianyulan"></use>
                </svg>
                <div class="name">15.图片预览</div>
                <div class="code-name">#icon-15tupianyulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-728bianjiqi_zitidaxiao"></use>
                </svg>
                <div class="name">728编辑器_字体大小</div>
                <div class="code-name">#icon-728bianjiqi_zitidaxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziti"></use>
                </svg>
                <div class="name">字体</div>
                <div class="code-name">#icon-ziti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-font-size"></use>
                </svg>
                <div class="name">字体大小</div>
                <div class="code-name">#icon-font-size</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuodong"></use>
                </svg>
                <div class="name">拖动</div>
                <div class="code-name">#icon-tuodong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongyingwen1"></use>
                </svg>
                <div class="name">中英文</div>
                <div class="code-name">#icon-zhongyingwen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuhao-yingwen"></use>
                </svg>
                <div class="name">符号-英文</div>
                <div class="code-name">#icon-fuhao-yingwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuhao-zhongwen"></use>
                </svg>
                <div class="name">符号-中文</div>
                <div class="code-name">#icon-fuhao-zhongwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diqiu"></use>
                </svg>
                <div class="name">地球</div>
                <div class="code-name">#icon-diqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingqiu"></use>
                </svg>
                <div class="name">星球</div>
                <div class="code-name">#icon-xingqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diqiu1"></use>
                </svg>
                <div class="name">地球</div>
                <div class="code-name">#icon-diqiu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huanjingxingqiu"></use>
                </svg>
                <div class="name">环境 星球</div>
                <div class="code-name">#icon-huanjingxingqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidingyibuju"></use>
                </svg>
                <div class="name">自定义布局</div>
                <div class="code-name">#icon-zidingyibuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayin"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#icon-dayin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-step"></use>
                </svg>
                <div class="name">步骤</div>
                <div class="code-name">#icon-step</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-30xuanzhongyuanxingfill"></use>
                </svg>
                <div class="name">30_选中圆形_fill</div>
                <div class="code-name">#icon-30xuanzhongyuanxingfill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shibai"></use>
                </svg>
                <div class="name">失败</div>
                <div class="code-name">#icon-shibai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-7_round_solid"></use>
                </svg>
                <div class="name">7_round_solid_数字7_by_climei</div>
                <div class="code-name">#icon-7_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-6_round_solid"></use>
                </svg>
                <div class="name">6_round_solid_数字6_by_climei</div>
                <div class="code-name">#icon-6_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-9_round_solid"></use>
                </svg>
                <div class="name">9_round_solid_数字9_by_climei</div>
                <div class="code-name">#icon-9_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-1_round_solid"></use>
                </svg>
                <div class="name">1_round_solid_数字1_by_climei</div>
                <div class="code-name">#icon-1_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-5_round_solid"></use>
                </svg>
                <div class="name">5_round_solid_数字5_by_climei</div>
                <div class="code-name">#icon-5_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-2_round_solid"></use>
                </svg>
                <div class="name">2_round_solid_数字2_by_climei</div>
                <div class="code-name">#icon-2_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-0_round_solid"></use>
                </svg>
                <div class="name">0_round_solid_数字0_by_climei</div>
                <div class="code-name">#icon-0_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-3_round_solid"></use>
                </svg>
                <div class="name">3_round_solid_数字3_by_climei</div>
                <div class="code-name">#icon-3_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-4_round_solid"></use>
                </svg>
                <div class="name">4_round_solid_数字4_by_climei</div>
                <div class="code-name">#icon-4_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-8_round_solid"></use>
                </svg>
                <div class="name">8_round_solid_数字8_by_climei</div>
                <div class="code-name">#icon-8_round_solid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-radio-off-full"></use>
                </svg>
                <div class="name">圆形</div>
                <div class="code-name">#icon-radio-off-full</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#icon-tongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ico"></use>
                </svg>
                <div class="name">二维码</div>
                <div class="code-name">#icon-ico</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chazhaobiaodanliebiao"></use>
                </svg>
                <div class="name">查找 表单 列表</div>
                <div class="code-name">#icon-chazhaobiaodanliebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaodan"></use>
                </svg>
                <div class="name">表单</div>
                <div class="code-name">#icon-biaodan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-siweidaotu"></use>
                </svg>
                <div class="name">思维导图</div>
                <div class="code-name">#icon-siweidaotu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiliandongxuanzeqi"></use>
                </svg>
                <div class="name">级联动选择器</div>
                <div class="code-name">#icon-jiliandongxuanzeqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caijian"></use>
                </svg>
                <div class="name">裁剪</div>
                <div class="code-name">#icon-caijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuwenben"></use>
                </svg>
                <div class="name">富文本</div>
                <div class="code-name">#icon-fuwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuwenbenkuang"></use>
                </svg>
                <div class="name">富文本框</div>
                <div class="code-name">#icon-fuwenbenkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangchuan"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#icon-shangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanzeqi"></use>
                </svg>
                <div class="name">选择器</div>
                <div class="code-name">#icon-xuanzeqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fangkuang"></use>
                </svg>
                <div class="name">方框</div>
                <div class="code-name">#icon-fangkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gouxuan-weixuanzhong-xianxingfangkuang"></use>
                </svg>
                <div class="name">79勾选-未选中-线性方框</div>
                <div class="code-name">#icon-gouxuan-weixuanzhong-xianxingfangkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shidu"></use>
                </svg>
                <div class="name">湿度</div>
                <div class="code-name">#icon-shidu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yangan"></use>
                </svg>
                <div class="name">烟感</div>
                <div class="code-name">#icon-yangan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendu"></use>
                </svg>
                <div class="name">温度</div>
                <div class="code-name">#icon-wendu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zaosheng"></use>
                </svg>
                <div class="name">噪声</div>
                <div class="code-name">#icon-zaosheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinridaiban"></use>
                </svg>
                <div class="name">今日待办</div>
                <div class="code-name">#icon-jinridaiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-AIshiyanshi"></use>
                </svg>
                <div class="name">13 AI 实验室</div>
                <div class="code-name">#icon-AIshiyanshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingkaiban"></use>
                </svg>
                <div class="name">申请开班</div>
                <div class="code-name">#icon-shenqingkaiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongyingwenqiehuan"></use>
                </svg>
                <div class="name">中英文切换</div>
                <div class="code-name">#icon-zhongyingwenqiehuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongyingwen"></use>
                </svg>
                <div class="name">中英文</div>
                <div class="code-name">#icon-zhongyingwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongyingzhuanhuan"></use>
                </svg>
                <div class="name">中英转换</div>
                <div class="code-name">#icon-zhongyingzhuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuju"></use>
                </svg>
                <div class="name">数据4</div>
                <div class="code-name">#icon-shuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ico_shuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#icon-ico_shuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuju1"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#icon-shuju1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhiyemian"></use>
                </svg>
                <div class="name">复制 页面</div>
                <div class="code-name">#icon-fuzhiyemian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caozuo-wailian"></use>
                </svg>
                <div class="name">操作-外链</div>
                <div class="code-name">#icon-caozuo-wailian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#icon-icon-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin"></use>
                </svg>
                <div class="name">个人中心</div>
                <div class="code-name">#icon-gerenzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xitongshezhi"></use>
                </svg>
                <div class="name">系统设置</div>
                <div class="code-name">#icon-xitongshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-neiqianshujuchucun"></use>
                </svg>
                <div class="name">内嵌数据储存</div>
                <div class="code-name">#icon-neiqianshujuchucun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanxian"></use>
                </svg>
                <div class="name">权限</div>
                <div class="code-name">#icon-quanxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zujian"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#icon-zujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-crew_feature"></use>
                </svg>
                <div class="name">功能</div>
                <div class="code-name">#icon-crew_feature</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongju"></use>
                </svg>
                <div class="name">工具</div>
                <div class="code-name">#icon-gongju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-skin"></use>
                </svg>
                <div class="name">皮肤</div>
                <div class="code-name">#icon-skin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shixinyuan"></use>
                </svg>
                <div class="name">实心圆</div>
                <div class="code-name">#icon-shixinyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-webicon318"></use>
                </svg>
                <div class="name">点选</div>
                <div class="code-name">#icon-webicon318</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dian"></use>
                </svg>
                <div class="name">点</div>
                <div class="code-name">#icon-dian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fullscreen"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-fullscreen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichuquanping"></use>
                </svg>
                <div class="name">退出全屏</div>
                <div class="code-name">#icon-tuichuquanping</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
