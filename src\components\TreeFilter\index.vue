<template>
  <div class="tree-main">
    <el-input v-model="filterText" placeholder="输入关键词" @input="handleFilter" clearable />
    <el-tree
      ref="treeRef"
      :data="data"
      :props="defaultProps"
      :filter-node-method="filterNode"
      :node-key="nodeKey" 
      default-expand-all
      highlight-current
    />
  </div>
</template>
<script setup name="TreeFilter">
import { ref } from 'vue'
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  nodeKey: { // 唯一标识符
    type: String,
    default: () => 'id'
  },
  filterChild: { // 是否查询子节点开关 true: 过滤掉子节点 false: 不过滤子节点
    type: Boolean,
    default: true
  },
})

const defaultProps = {
  label: 'label',
  children: 'children'
}

const filterText = ref('')
const treeRef = ref()

const filterNode = (value, data, node) => {
  if (!value) return true
  if (!props.filterChild) {
    // 核心：当前节点或其任一祖先匹配则返回 true
    const keyword = value.toLowerCase()
    const selfMatch = data.label.toLowerCase().includes(keyword)
    if (selfMatch) return true
    // 往上查找父节点
    let parent = node.parent
    while (parent) {
      const label = parent.data?.label?.toLowerCase() || ''
      if (label.includes(keyword)) return true
      parent = parent.parent
    }
    return false
  } else {
    return data.label.includes(value)
  }
}

// 调用树的 filter 方法
const handleFilter = () => {
  treeRef.value?.filter(filterText.value)
}
</script>
<style lang="scss">
</style>