<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="system-content-area">
				<el-collapse v-model="state.activeName">
					<el-collapse-item title="自动宕机迁移" name="1">
						<Migration></Migration>
					</el-collapse-item>
				</el-collapse>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts" name="Secure">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
const Migration = defineAsyncComponent(() => import('./Migration.vue'));

const state = reactive({
	activeName: ['1','2','3','4'],
});

onMounted(() => {})
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .system-content-area {
    width: 100%;
    height: 100%;
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>


