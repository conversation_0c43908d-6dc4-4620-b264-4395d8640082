<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
      </div>
      <div v-if="powerItem.sousuo">
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area" v-if="powerItem.liebiao">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
			>
			</my-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { vmAlarmQuery } from '/@/api/ResourcePool'; // 接口
import { alarmColumns } from '/@/model/resource'; // 表列、正则

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: alarmColumns as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  if(true) {
    return {
      data: [{name:'测试1',remark:'备注1',id:'aa1'},{name:'测试2',remark:'备注2',id:'aa2'}], // 数据
      total: 2 // 总数
    }
  }
  return new Promise(async(resolve)=>{
    vmAlarmQuery({
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
const tableRef = ref();
// 刷新
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
watch(
  ()=> props.treeItem,
  (val)=>{
    refresh()
  }
);
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'gaojingliebiao',
    'gaojingsousuo',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.gaojingliebiao;
		powerItem.sousuo = res.data.gaojingsousuo;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>