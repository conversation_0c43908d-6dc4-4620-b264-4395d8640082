<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="添加物理机"
    class="dialog-500"
  >
  <!-- 不在使用 -->
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="物理机名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入物理机名称"/>
      </el-form-item>
      <el-form-item label="物理机IP" prop="ip">
        <el-input v-model="formItem.ip"  placeholder="请输入物理机IP"/>
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="formItem.remark" :rows="2" maxlength="50" show-word-limit type="textarea" placeholder="请输入备注信息"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { treeAddHost } from '/@/api/ResourcePool'; // 接口
import { propName,propIP } from '/@/model/resource.js'; // 表列、正则


const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  addHostTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
  ip: '',
  remark: '',
});
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" },
  ],
  ip: [
    { required: true, message: '必填项' },
    { validator: propIP, trigger: "blur" },
  ]
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        treeAddHost({
          cluster_id: props.treeItem.id,
          ip: formItem.ip,
          name: formItem.name,
          remark: formItem.remark,
        })
        .then(res => {
          if(res.code == 200) {
            ElMessage.success(res.msg)
            emit('returnOK', 'refresh');
          }else{
            ElMessage.error(res.msg)
          }
        })
      }
    })
  }
}
watch(
  ()=> props.addHostTime,
  (val)=>{
    formItem.isShow = true;
    formItem.remark = ''
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);
</script>