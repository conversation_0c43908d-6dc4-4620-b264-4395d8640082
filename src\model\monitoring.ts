const chartColor = [
	'#F32370',
	'#2BC043',
	'#1684FC',
	'#386aff',
	'#ff950a',
	'#a042ed',
	'#f06361',
	'#ff8400',
	'#fd00ff',
	'#5f5449',
	'#fd4882',
	'#2bc199',
	'#00ffc6',
	'#8fff00',
	'#fff900',
	'#b0ff5c',
	'#ffd65c',
	'#00b6ff',
	'#ffa95c',
	'#f37542',
	'#f34242',
	'#f348fd',
	'#4d00ff',
	'#b148fd',
	'#5548fd',
	'#b36464',
	'#c12b31',
	'#c12b9f',
	'#892bc1',
	'#3c2bc1',
	'#2b66c1',
	'#2ba4c1',
	'#bfbdbb',
	'#2bc154',
	'#71c12b',
	'#b6c12b',
	'#c1a92b',
	'#c16b2b',
	'#c13a2b',
	'#ff0015',
	'#ff0097',
];
const upAndDown = (type: string, name: any) => {
	let sx = name.split('上行');
	let xx = name.split('下行');
	if (type == 'text') {
		if (sx.length == 2) {
			return '上行';
		} else if (xx.length == 2) {
			return '下行';
		} else {
			return '';
		}
	} else if (type == 'color') {
		if (sx.length == 2) {
			return '#216fed';
		} else if (xx.length == 2) {
			return '#37d159';
		} else {
			return '';
		}
	} else {
		if (sx.length == 2) {
			return sx[0];
		} else if (xx.length == 2) {
			return xx[0];
		} else {
			return name;
		}
	}
};
const formatUnit = (unit: string, size: any) => {
	if (unit == '%') {
		return unit;
	} else if (unit == 'io/s') {
		let number = size;
		size < 0 ? (number = size * -1) : (number = size);
		const units = ['', 'K', 'M', 'G', 'T', 'P'];
		let unitIndex = 0;
		while (number >= 1024 && unitIndex < units.length - 1) {
			number /= 1024;
			unitIndex++;
		}
		return units[unitIndex];
	} else {
		let number = size;
		size < 0 ? (number = size * -1) : (number = size);
		const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
		let unitIndex = 0;
		while (number >= 1024 && unitIndex < units.length - 1) {
			number /= 1024;
			unitIndex++;
		}
		return units[unitIndex];
	}
};
const formatNumber = (unit: string, size: any) => {
	if (unit == '%') {
		return Math.floor(size * 100) / 100;
	} else if (unit == 'io/s') {
		let number = size;
		size < 0 ? (number = size * -1) : (number = size);
		const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
		let unitIndex = 0;
		while (number >= 1024 && unitIndex < units.length - 1) {
			number /= 1024;
			unitIndex++;
		}
		return size < 0 ? (Math.floor(number * 100) / 100) * -1 : Math.floor(number * 100) / 100;
	} else {
		let number = size;
		size < 0 ? (number = size * -1) : (number = size);
		const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
		let unitIndex = 0;
		while (number >= 1024 && unitIndex < units.length - 1) {
			number /= 1024;
			unitIndex++;
		}
		return size < 0 ? (Math.floor(number * 100) / 100) * -1 : Math.floor(number * 100) / 100;
	}
};
const formatTime = (time: number, data: any) => {
	let ti = new Array();
	data.forEach((em: any) => {
		let date = new Date(em * 1000); // 将秒转换为毫秒
		let year = date.getFullYear();
		let month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加 1
		let day = String(date.getDate()).padStart(2, '0');
		let hours = String(date.getHours()).padStart(2, '0');
		let minutes = String(date.getMinutes()).padStart(2, '0');
		let seconds = String(date.getSeconds()).padStart(2, '0');
		if (time * 1 <= 24) {
			ti.push(hours + ':' + minutes);
		} else {
			ti.push(month + '-' + day);
		}
	});
	return ti;
};
const selectTime = (time: number) => {
	let current = new Date(); // 获取当前时间
	let start = new Date(current.getTime() - time * 60 * 60 * 1000); // 获取N小时前的时间
	let year = start.getFullYear(); // 获取年
	let month = ('0' + (start.getMonth() + 1)).slice(-2); // 获取月-月份从0开始，所以要加1
	let day = ('0' + start.getDate()).slice(-2); // 获取日
	let hours = ('0' + start.getHours()).slice(-2); // 获取时
	let minutes = start.getMinutes(); // 获取分
	let divide = '';

	// 调整分钟（可以/5的分钟）
	if (minutes % 10 < 5) {
		minutes = Math.floor(minutes / 10) * 10; // 当分钟尾数小于5时，取整为10的倍数
	} else {
		minutes = Math.ceil(minutes / 10) * 10 - 5; // 当分钟尾数大于6时，取整为10的倍数再减去5
	}
	divide = ('0' + minutes).slice(-2); // 格式化分钟

	// 输出一小时前的时间结果
	return year + '-' + month + '-' + day + ' ' + hours + ':' + divide; // 起始时间
};
let listvmQuery = [
	{
		hostname: 'controller1',
		ip: '***********',
		vm_count: 10,
		count_normal: 3, // 正常
		count_normal_list: [{ name: 'list1', ip: '***********', rely: false }],
		count_shutoff: 3, // 关机
		count_shutoff_list: [{ name: 'list2', ip: '***********' }],
		count_alarm: 3, // 告警
		count_alarm_list: [{ name: 'list3', ip: '***********' }],
		count_fault: 1, // 故障
		count_fault_list: [{ name: 'list4', ip: '***********' }],
		trend: 'cpu', // 默认显示图表
		time: 1, // 默认图表时间段
		chatdata: {},
		chartShwo: false,
	},
	{
		hostname: 'controller2',
		ip: '***********',
		vm_count: 4,
		count_normal: 1, // 正常
		count_normal_list: [{ name: 'list5', ip: '***********' }],
		count_shutoff: 1, // 关机
		count_shutoff_list: [{ name: 'list6', ip: '***********' }],
		count_alarm: 1, // 告警
		count_alarm_list: [{ name: 'list7', ip: '***********' }],
		count_fault: 1, // 故障
		count_fault_list: [{ name: 'list8', ip: '***********' }],
		trend: 'cpu', // 默认显示图表
		time: 1, // 默认图表时间段
		chatdata: {},
		chartShwo: false,
	},
];
// 字节+单位转换
const formatFileSize = (size: any) => {
	const units = ['MB', 'GB', 'TB', 'PB'];
	let unitIndex = 0;
	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}
	return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
};
// 字节+单位转换
const translation = (size: number) => {
	const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	let unitIndex = 0;
	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}
	return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
};
export { listvmQuery, chartColor, upAndDown, formatUnit, formatNumber, formatTime, selectTime, formatFileSize, translation };
