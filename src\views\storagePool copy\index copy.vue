<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn">
							<el-radio-group v-model="state.type" size="large">
								<el-radio-button label="接入" value="jeru" />
								<el-radio-button label="存储池" value="ccc" />
							</el-radio-group>
							<!-- <el-button type="primary" plain @click="treeData"
								><el-icon><RefreshRight /></el-icon
							></el-button> -->
							<!-- <el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input> -->
						</div>
						<div class="tree-area" v-if="state.type == 'ccc'">
							<ul id="treeDemo" class="ztree"></ul>
							<!-- <div id="rMenu">
                <ul class="mouse_right_box">
                  <li v-for="(item,index) in mouseData" :key='index' @click="mouseItem(item)">{{item}}</li>
                </ul>
              </div> -->
						</div>
						<div v-if="state.type == 'jeru'">
							<el-menu
								default-active="1"
								class="el-menu-vertical-demo"
								@open="handleOpen"
								@close="handleClose"
							>
								<el-menu-item index="1">
									<template #title>
										<span>IPsan</span>
									</template>
								</el-menu-item>
								<el-menu-item index="2">
									<template #title>
										<span>FCsan</span>
									</template>
								</el-menu-item>
								<el-menu-item index="3">
									<template #title>
										<span>NVS</span>
									</template>
								</el-menu-item>
							</el-menu>
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<!-- 资源节点 -->
					<!-- <ResourceNode v-if="state.treeItem.level == '0'" :treeItem="state.treeItem" @returnOK="returnOK"></ResourceNode> -->
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口

// 引入组件
// const ResourceNode = defineAsyncComponent(() => import('./resourceNode/index.vue'));

// 定义变量内容
const searchRef = ref();
const state = reactive({
	type: 'jeru',
	treeItem: {
		id: '1',
	},
	searchValue: '',
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
// 定义图标
const customizeIcon = (treeId: string, treeNode:{tId: string,level:number} ) => {
  let $icon = $("#" + treeNode.tId + "_ico");
  const icons = [ic0, ic1, ic2, ic3, ic4];
  const icon = icons[treeNode.level]; // 根据 level 获取对应图标
  if (icon) {
    $icon.css({
      background: `url(${icon}) no-repeat center center`,
      backgroundSize: '100% 100%',
    });
  }
};
// 点击节点
const onNodeClick = (event: any, treeId: string, treeNode: any) => {
	state.treeItem = treeNode;
};
// 右键点击
const onRightClick = () => {};
// 拖拽
const onDrop = () => {};
// 菜单打开
const handleOpen = () => {};
// 菜单关闭
const handleClose = () => {};

const setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
	},
	view: {
		showIcon: false, // 显示图标
		addDiyDom: customizeIcon, // 自定义图标
		// expandSpeed: "fast",
    fontCss: (treeId:any, treeNode:any) => (treeNode.highlight ? { color: 'red', 'font-weight': 'bold' } : { color: '#333' }),
	},
	callback: {
		onClick: onNodeClick,
		onRightClick: onRightClick,
		onDrop: onDrop,
	},
};
interface Node {
	id: string;
	name: string;
	pid: string;
}
let zNodes: Node[] = [];
const treeData = (item: string) => {
  state.searchValue = ''
	if (true) {
		zNodes = [
			{ id: '1', name: '资源节点', pid: '0' },
			{ id: '2', name: '主机池1', pid: '1' },
			{ id: '3', name: '集群1', pid: '2' },
			{ id: '4', name: '主机1', pid: '3' },
			{ id: '5', name: '主机2', pid: '3' },
			{ id: '6', name: '主机3', pid: '3' },
			{ id: '7', name: 'vm1', pid: '4' },
			{ id: '8', name: 'vm2', pid: '4' },
			{ id: '9', name: 'vm3', pid: '5' },
			{ id: '10', name: 'vm4', pid: '5' },
			{ id: '10', name: 'vm5', pid: '5' },
			{ id: '10', name: 'vm3', pid: '5' },
		];
		init(item);
	} else {
		resourceTreeQuery()
			.then((res) => {
				zNodes = res.data;
				init(item);
			})
			.catch((error) => {});
	}
};
const init = (item: string) => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
	zTreeObj.expandAll(true); // 默认展开所有树的分支
	let treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	if (item == 'delete') {
		treeNode = zTreeObj.getNodeByParam('id', zNodes[0].id);
	} else {
		treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	}
	// 高亮选中根节点
	zTreeObj.selectNode(treeNode);
	state.treeItem = treeNode;
};
const returnOK = (item: string) => {
	treeData(item);
};
const onSearch = () => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
  if (!zTreeObj) return;
	const value = state.searchValue.trim();
	const allNodes = zTreeObj.transformToArray(zTreeObj.getNodes());

	// 清除所有节点的高亮状态
	allNodes.forEach((node:any) => {
		node.highlight = false;
		zTreeObj.updateNode(node);
	});

	// 如果搜索值为空，直接返回
	if (!value) return;

	// 获取匹配的节点
	const matchedNodes = zTreeObj.getNodesByParamFuzzy('name', value);

	// 高亮匹配的节点并展开父节点
	matchedNodes.forEach((node:any) => {
		node.highlight = true;
		zTreeObj.updateNode(node);
		zTreeObj.expandNode(node.getParentNode(), true);
	});
};
watch(
  ()=> state.type,
  (val)=>{
    if(val == 'ccc') {
			setTimeout(() => {
				treeData('');
			}, 500);
		}else {

		}
  }
);
// 页面加载时
onMounted(() => {
	setTimeout(() => {
		treeData('');
	}, 500);
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 220px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 15px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
				#rMenu {
					position: absolute;
					visibility: hidden;
					background-color: var(--el-fill-color-blank);
					z-index: 1;
					border-radius: 4px;
					box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
					.mouse_right_box {
						padding: 5px 0;
						li {
							margin: 0;
							line-height: normal;
							padding: 7px 16px;
							clear: both;
							color: #515a6e;
							cursor: pointer;
							font-size: 14px !important;
						}
						li:hover {
							background-color: #f5f5f5;
						}
					}
				}
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 240px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
.el-menu-vertical-demo {
	width: 100%;
	.el-menu-item.is-active {
		color: var(--next-color-white) !important;
	}
}
</style>
